<?xml version="1.0"?>
<launch>
	<!-- use robot pose ekf to provide odometry-->
	<node pkg="robot_pose_ekf" name="robot_pose_ekf" type="robot_pose_ekf">
		<param name="output_frame" value="odom" />
		<param name="base_footprint_frame" value="base_footprint"/>
		<remap from="imu_data" to="/limo/imu" />
	</node>

	<node pkg="amcl" type="amcl" name="amcl" output="screen">
		<rosparam file="$(find limo_bringup)/param/amcl_params_diff.yaml" command="load" />
		<param name="initial_pose_x" value="0"/>
		<param name="initial_pose_y" value="0"/>
		<param name="initial_pose_a" value="0"/>
		<remap from="/scan" to="/limo/scan" />
		   
		<!-- 设置坐标系 -->
  		<param name="odom_frame_id" value="odom"/><!-- 里程计坐标系 -->
  		<param name="base_frame_id" value="base_footprint"/><!-- 添加机器人基坐标系 -->
  		<param name="global_frame_id" value="map"/><!-- 添加地图坐标系 -->
	</node>


	<node pkg="map_server" type="map_server" name="map_server" args="$(find limo_bringup)/maps/nav.yaml" output="screen">
		<param name="frame_id" value="map"/>
	</node>
	
	<!--  ************** Navigation ***************  -->
	<node pkg="move_base" type="move_base" respawn="false" name="move_base" output="screen">
		<rosparam file="$(find limo_bringup)/param/ackerman/costmap_common_params.yaml" command="load" ns="global_costmap" />
		<rosparam file="$(find limo_bringup)/param/ackerman/costmap_common_params.yaml" command="load" ns="local_costmap" />
		<rosparam file="$(find limo_bringup)/param/ackerman/local_costmap_params.yaml" command="load" />
		<rosparam file="$(find limo_bringup)/param/ackerman/global_costmap_params.yaml" command="load" />
		<rosparam file="$(find limo_bringup)/param/ackerman/teb_local_planner_params.yaml" command="load" />

		<!-- <param name="base_global_planner" value="global_planner/GlobalPlanner" /> -->
		<!-- <param name="planner_frequency" value="1.0" /> -->
		<!-- <param name="planner_patience" value="5.0" /> -->
		<!-- <param name="base_local_planner" value="teb_local_planner/TebLocalPlannerROS" /> -->
		<!-- <param name="controller_frequency" value="5.0" /> -->
		<!-- <param name="controller_patience" value="15.0" /> -->
		<!-- <param name="clearing_rotation_allowed" value="false" /> -->


		<!-- <remap from="/scan" to="/limo/scan" /> -->
		<!-- Our carlike robot is not able to rotate in place -->
	</node>

	<!--  **************** Visualization ****************  -->
	<node name="rviz" pkg="rviz" type="rviz" args="-d $(find limo_bringup)/rviz/navigation_ackerman.rviz"/>

</launch>

