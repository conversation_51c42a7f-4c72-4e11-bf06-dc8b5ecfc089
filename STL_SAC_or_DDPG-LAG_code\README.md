# Deep reinforcement learning under signal temporal logic constraints using Lagrangian relaxiation

Implementation of Deep reinforcement learning under signal temporal logic constraints using Lagrangian relaxiation https://arxiv.org/abs/2201.08504.

## Python Version
```
Python 3.6.9
Cuda 10.2
Pytorch 1.7.1
Torch Audio 0.7.2
Torch Vision 0.8.2
gym 0.19.0

```

## PC
```
CPU: AMD(R) Ryzen 9 3950X
Main Memory: DDR4-2666 16GB 2
Motherboard: ASUS PRIME X570-PRO
GPU: NVIDIA(R) GeForce RTX 2070 SUPER
OS: ubuntu 18.04
```

## Pip List
```
cloudpickle (1.6.0)
cycler (0.11.0)
dataclasses (0.8)
future (0.18.2)
gym (0.19.0)
importlib-metadata (4.8.3)
kiwisolver (1.3.1)
matplotlib (3.3.4)
numpy (1.19.5)
pandas (1.1.5)
Pillow (7.2.0)
pip (9.0.1)
pkg-resources (0.0.0)
pyglet (1.5.0)
pyparsing (3.0.6)
python-dateutil (2.8.2)
pytz (2021.3)
scipy (1.5.4)
setuptools (39.0.1)
six (1.16.0)
torch (1.7.1)
torchaudio (0.7.2)
torchvision (0.8.2)
typing-extensions (4.0.1)
zipp (3.6.0)
```
