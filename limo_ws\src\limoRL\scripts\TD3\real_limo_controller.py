#!/usr/bin/env python3

import rospy
import numpy as np
import torch
import time
import json
from std_msgs.msg import String, Bool
from geometry_msgs.msg import PoseStamped
from real_limo_environment import RealLimoEnvironment
from lagrangian_td3 import LagrangianTD3
import argparse
import os

class RealLimoController:
    """
    实物Limo机器人STL任务控制器
    """
    
    def __init__(self, model_path: str, stl_task: str = "basic_visit", 
                 use_amcl: bool = True, control_frequency: float = 10.0):
        """
        初始化控制器
        
        :param model_path: 训练好的模型路径
        :param stl_task: STL任务名称
        :param use_amcl: 是否使用AMCL定位
        :param control_frequency: 控制频率(Hz)
        """
        self.model_path = model_path
        self.control_frequency = control_frequency
        
        # 初始化环境
        self.env = RealLimoEnvironment(stl_task, use_amcl)
        
        # 加载训练好的模型
        self.agent = self.load_trained_model()
        
        # 控制状态
        self.is_running = False
        self.past_action = [0.0, 0.0]
        self.step_count = 0
        self.start_time = None
        
        # 性能统计
        self.performance_stats = {
            "total_steps": 0,
            "regions_visited": set(),
            "min_obstacle_distance": float('inf'),
            "task_completion_time": None,
            "collision_occurred": False
        }
        
        # ROS通信
        self.setup_ros_communication()
        
        print("实物Limo控制器初始化完成")
        print(f"模型路径: {model_path}")
        print(f"STL任务: {stl_task}")
        print(f"控制频率: {control_frequency} Hz")
    
    def setup_ros_communication(self):
        """设置ROS通信"""
        # 发布者
        self.performance_pub = rospy.Publisher('/limo_performance', String, queue_size=1)
        self.control_status_pub = rospy.Publisher('/limo_control_status', String, queue_size=1)
        
        # 订阅者
        self.start_stop_sub = rospy.Subscriber('/limo_start_stop', Bool, self.start_stop_callback)
        self.goal_sub = rospy.Subscriber('/move_base_simple/goal', PoseStamped, self.goal_callback)
    
    def load_trained_model(self):
        """加载训练好的模型"""
        try:
            # 模型参数（需要与训练时一致）
            state_dim = 30
            action_dim = 2
            actor_fc1_dim = 512
            actor_fc2_dim = 512
            critic_fc1_dim = 512
            critic_fc2_dim = 512
            alpha = 3e-4
            beta = 3e-4
            lr_kappa = 1e-5
            action_limit_v = 0.8
            action_limit_w = 1.8
            threshold = -40.0
            pretrain_steps = 250000
            
            # 创建算法实例
            agent = LagrangianTD3(
                alpha=alpha,
                beta=beta,
                state_dim=state_dim,
                action_dim=action_dim,
                actor_fc1_dim=actor_fc1_dim,
                actor_fc2_dim=actor_fc2_dim,
                critic_fc1_dim=critic_fc1_dim,
                critic_fc2_dim=critic_fc2_dim,
                ckpt_dir=os.path.dirname(self.model_path),
                action_limit_v=action_limit_v,
                action_limit_w=action_limit_w,
                lr_kappa=lr_kappa,
                threshold=threshold,
                pretrain_steps=pretrain_steps
            )
            
            # 加载模型权重
            model_episode = os.path.basename(self.model_path).split('_')[-1].split('.')[0]
            agent.load_models(model_episode)
            
            print(f"成功加载模型: {self.model_path}")
            return agent
            
        except Exception as e:
            print(f"加载模型失败: {e}")
            return None
    
    def start_stop_callback(self, data: Bool):
        """开始/停止控制回调"""
        if data.data and not self.is_running:
            self.start_control()
        elif not data.data and self.is_running:
            self.stop_control()
    
    def goal_callback(self, data: PoseStamped):
        """目标点回调（可用于动态设置目标）"""
        print(f"接收到新目标点: ({data.pose.position.x:.2f}, {data.pose.position.y:.2f})")
        # 这里可以根据需要更新目标点
    
    def start_control(self):
        """开始控制"""
        if self.agent is None:
            print("模型未加载，无法开始控制")
            return
        
        self.is_running = True
        self.start_time = time.time()
        self.step_count = 0
        self.performance_stats = {
            "total_steps": 0,
            "regions_visited": set(),
            "min_obstacle_distance": float('inf'),
            "task_completion_time": None,
            "collision_occurred": False
        }
        
        print("开始STL任务控制")
        self.publish_control_status("STARTED")
    
    def stop_control(self):
        """停止控制"""
        self.is_running = False
        self.env.stop_robot()
        
        # 计算任务完成时间
        if self.start_time:
            self.performance_stats["task_completion_time"] = time.time() - self.start_time
        
        print("停止STL任务控制")
        self.publish_control_status("STOPPED")
        self.publish_performance_stats()
    
    def control_step(self):
        """执行一步控制"""
        if not self.is_running or self.agent is None:
            return
        
        try:
            # 获取当前状态
            state = self.env.get_state(self.past_action)
            
            # 使用模型选择动作
            action = self.agent.choose_action(state, train=False)
            
            # 执行动作
            self.env.execute_action(action)
            
            # 更新统计信息
            self.update_performance_stats(state, action)
            
            # 更新过去动作
            self.past_action = action.tolist()
            self.step_count += 1
            
            # 检查任务完成
            if self.env.check_stl_completion():
                print("STL任务完成!")
                self.stop_control()
                return
            
            # 检查碰撞
            if self.env.collision_detected:
                print("检测到碰撞，停止控制")
                self.performance_stats["collision_occurred"] = True
                self.stop_control()
                return
            
            # 发布状态
            self.env.publish_task_status()
            
        except Exception as e:
            print(f"控制步骤出错: {e}")
            self.stop_control()
    
    def update_performance_stats(self, state: np.ndarray, action: np.ndarray):
        """更新性能统计"""
        self.performance_stats["total_steps"] += 1
        
        # 更新最小障碍物距离
        min_obstacle_dist = state[22]  # 索引22是最小障碍物距离
        self.performance_stats["min_obstacle_distance"] = min(
            self.performance_stats["min_obstacle_distance"], 
            min_obstacle_dist
        )
        
        # 检查访问的区域
        if self.env.position and self.env.stl_regions:
            for i, region in enumerate(self.env.stl_regions):
                if region.is_inside(self.env.position.x, self.env.position.y):
                    self.performance_stats["regions_visited"].add(i)
    
    def publish_control_status(self, status: str):
        """发布控制状态"""
        status_data = {
            "status": status,
            "step_count": self.step_count,
            "elapsed_time": time.time() - self.start_time if self.start_time else 0,
            "stl_task": self.env.stl_config["description"] if self.env.stl_config else "Unknown"
        }
        
        msg = String()
        msg.data = json.dumps(status_data)
        self.control_status_pub.publish(msg)
    
    def publish_performance_stats(self):
        """发布性能统计"""
        stats = self.performance_stats.copy()
        stats["regions_visited"] = list(stats["regions_visited"])
        
        msg = String()
        msg.data = json.dumps(stats)
        self.performance_pub.publish(msg)
        
        # 打印统计信息
        print("\n=== 性能统计 ===")
        print(f"总步数: {stats['total_steps']}")
        print(f"访问区域: {stats['regions_visited']}")
        print(f"最小障碍物距离: {stats['min_obstacle_distance']:.3f}m")
        print(f"任务完成时间: {stats['task_completion_time']:.2f}s" if stats['task_completion_time'] else "未完成")
        print(f"是否碰撞: {'是' if stats['collision_occurred'] else '否'}")
    
    def run(self):
        """运行控制器"""
        rate = rospy.Rate(self.control_frequency)
        
        print("控制器就绪，等待开始命令...")
        print("发布消息到 /limo_start_stop 开始/停止控制")
        
        while not rospy.is_shutdown():
            if self.is_running:
                self.control_step()
            
            rate.sleep()

def main():
    parser = argparse.ArgumentParser(description='实物Limo机器人STL任务控制器')
    parser.add_argument('--model', required=True, help='训练好的模型路径')
    parser.add_argument('--task', default='basic_visit', help='STL任务名称')
    parser.add_argument('--amcl', action='store_true', help='使用AMCL定位')
    parser.add_argument('--freq', type=float, default=10.0, help='控制频率(Hz)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.model):
        print(f"模型文件不存在: {args.model}")
        return
    
    try:
        # 创建控制器
        controller = RealLimoController(
            model_path=args.model,
            stl_task=args.task,
            use_amcl=args.amcl,
            control_frequency=args.freq
        )
        
        # 运行控制器
        controller.run()
        
    except rospy.ROSInterruptException:
        print("程序被中断")
    except KeyboardInterrupt:
        print("用户中断程序")

if __name__ == "__main__":
    main()
