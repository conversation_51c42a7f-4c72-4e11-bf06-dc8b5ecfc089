<?xml version='1.0'?>
<sdf version='1.6'>
  <model name='turtlebot3_square'>
    <pose frame=''>0 0 0 0 -0 0</pose>
    <link name='Wall_0'>
      <collision name='Wall_0_Collision'>
        <geometry>
          <box>
            <size>4 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_0_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>4 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>0 1.925 0 0 -0 0</pose>
    </link>
    <link name='Wall_2'>
      <collision name='Wall_2_Collision'>
        <geometry>
          <box>
            <size>4 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_2_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>4 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>-1.925 0 0 0 0 -1.5708</pose>
    </link>
    <link name='Wall_3'>
      <collision name='Wall_3_Collision'>
        <geometry>
          <box>
            <size>4 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_3_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>4 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>0 -1.925 0 0 -0 0</pose>
    </link>
    <link name='Wall_4'>
      <collision name='Wall_4_Collision'>
        <geometry>
          <box>
            <size>4 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_4_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>4 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>1.925 0 0 0 -0 1.5708</pose>
    </link>
    <static>1</static>
  </model>
</sdf>
