#!/usr/bin/python3
# 导入 ROS 的 Python 客户端库，用于与 ROS 系统进行交互
import rospy
# 导入 NumPy 库，用于高效的数值计算
import numpy as np
# 导入 Python 内置的数学库，提供基本的数学函数
import math
# 导入 copy 模块，用于对象的深拷贝操作
import copy
# 导入 time 模块，用于时间相关的操作
import time
# 从 math 模块中导入圆周率 pi
from math import pi
# 从当前目录下的 respawnGoal 模块中导入 Respawn 类，用于目标点的重置
from .respawnGoal import Respawn
# 从 geometry_msgs.msg 模块中导入 Twist、Point 和 Pose 消息类型
from geometry_msgs.msg import Twist, Point, Pose
# 从 sensor_msgs.msg 模块中导入 LaserScan 消息类型，用于获取激光雷达数据
from sensor_msgs.msg import LaserScan
# 从 nav_msgs.msg 模块中导入 Odometry 消息类型，用于获取机器人的里程计信息
from nav_msgs.msg import Odometry
# 从 std_srvs.srv 模块中导入 Empty 服务类型，用于调用空参数的服务
from std_srvs.srv import Empty
# 从 tf.transformations 模块中导入 euler_from_quaternion 和 quaternion_from_euler 函数，用于四元数和欧拉角的转换
from tf.transformations import euler_from_quaternion, quaternion_from_euler


class Env():
    """
    该类用于创建一个强化学习环境，模拟机器人在 Gazebo 仿真环境中的导航任务。
    包含环境初始化、状态获取、奖励计算、动作执行和环境重置等功能。
    """
    def __init__(self,action_dim = 2):
        """
        初始化环境类。

        :param action_dim: 动作空间的维度，默认为 2
        """
        # 目标点的 x 坐标
        self.goal_x = 0
        # 目标点的 y 坐标
        self.goal_y = 0
        # 机器人朝向目标的角度
        self.heading = 0
        # 标记是否需要初始化目标点
        self.initGoal = True
        # 标记是否到达目标点
        self.get_goalbox = False
        
        # 机器人的位置，初始化为 Pose 消息类型
        self.position = Pose()
        # 创建一个 ROS 发布者，用于发布速度指令到 'cmd_vel' 话题
        self.pub_cmd_vel = rospy.Publisher('cmd_vel', Twist, queue_size=5)
        # 创建一个 ROS 订阅者，订阅 'odom' 话题的里程计信息，并调用 getOdometry 方法处理
        self.sub_odom = rospy.Subscriber('odom', Odometry, self.getOdometry)
        # 创建一个 ROS 服务代理，用于调用 '/gazebo/reset_world' 服务重置仿真环境
        self.reset_proxy = rospy.ServiceProxy('/gazebo/reset_world', Empty)
        # 创建一个 ROS 服务代理，用于调用 'gazebo/unpause_physics' 服务恢复物理仿真
        self.unpause_proxy = rospy.ServiceProxy('gazebo/unpause_physics', Empty)
        # 创建一个 ROS 服务代理，用于调用 'gazebo/pause_physics' 服务暂停物理仿真
        self.pause_proxy = rospy.ServiceProxy('gazebo/pause_physics', Empty)
        # 实例化 Respawn 类，用于目标点的重置
        self.respawn_goal = Respawn()
        
        # 上一次的距离
        self.last_distance = 0
        # 过去的距离
        self.past_distance = 0.
        # 初始距离
        self.initial_diatance = 0.
        # 标记机器人是否停止
        self.stopped = 0
        # 动作空间的维度
        self.action_dim = action_dim

        # 注释掉的变量，可用于记录上一次的 x 和 y 方向差距
        # self.x_gap_last = 0
        # self.y_gap_last = 0
        
        # 注册一个回调函数，当程序接收到关闭信号（如 CTRL + c）时调用 shutdown 方法
        rospy.on_shutdown(self.shutdown)
        
    def shutdown(self):
        """
        程序关闭时的回调函数，用于停止机器人运动。
        """
        # 记录日志信息，表示正在停止 TurtleBot
        rospy.loginfo("Stopping TurtleBot")
        # 发布零速度指令，使机器人停止运动
        self.pub_cmd_vel.publish(Twist())
        # 程序休眠 1 秒
        rospy.sleep(1)
    
    def getGoalDistace(self):
        """
        计算机器人当前位置到目标点的距离。

        :return: 四舍五入保留两位小数后的目标距离
        """
        # 计算机器人当前位置到目标点的欧几里得距离，并四舍五入保留两位小数
        goal_distance = round(math.hypot(self.goal_x - self.position.x, self.goal_y - self.position.y), 2)
        # 更新过去的距离
        self.past_distance = goal_distance
        # 更新初始距离
        self.initial_diatance = goal_distance
        return goal_distance
    
    def getOdometry(self, odom):
        """
        处理接收到的里程计信息，更新机器人的位置和朝向。

        :param odom: 接收到的 Odometry 消息
        """
        # 深拷贝上一次的位置信息
        self.past_position = copy.deepcopy(self.position)
        # 更新机器人的位置信息
        self.position = odom.pose.pose.position
        # 获取机器人的朝向（四元数表示）
        orientation = odom.pose.pose.orientation
        # 将四元数转换为列表
        orientation_list = [orientation.x, orientation.y, orientation.z, orientation.w]
        # 将四元数转换为欧拉角，只取 yaw 角
        _, _, yaw = euler_from_quaternion(orientation_list)
        
        # 计算机器人到目标点的角度
        goal_angle = math.atan2(self.goal_y - self.position.y, self.goal_x - self.position.x)
        # 计算机器人朝向目标的角度
        heading = goal_angle - yaw
        
        # 将 heading 角度归一化到 [-pi, pi] 范围内
        if heading > pi:
            heading -= 2 * pi

        elif heading < -pi:
            heading += 2 * pi

        # 四舍五入保留三位小数后更新机器人朝向目标的角度
        self.heading = round(heading, 3)
        
    def getState(self, scan,past_action):
        """
        根据激光雷达数据和上一次的动作获取当前环境状态。

        :param scan: 接收到的 LaserScan 消息
        :param past_action: 上一次执行的动作
        :return: 当前环境状态列表和是否结束的标志
        """
        # state 包含 20 个激光雷达数据 + heading + current_disctance + obstacle_min_range, obstacle_angle 
        
        # 存储处理后的激光雷达数据
        scan_range = []
        # 获取机器人朝向目标的角度
        heading = self.heading
        # 最小安全距离
        min_range = 0.20
        # 标记是否结束
        done = False

        # 遍历激光雷达数据
        for i in range(len(scan.ranges)):
            if scan.ranges[i] == float('Inf') or scan.ranges[i] >3.5:
                # 若激光雷达数据为无穷大或超过 3.5，将其替换为 3.5
                scan_range.append(3.5)
            elif np.isnan(scan.ranges[i]):
                # 若激光雷达数据为 NaN，将其替换为 0
                scan_range.append(0)
            else:
                # 否则直接添加原始数据
                scan_range.append(scan.ranges[i])
        
        # 计算最小障碍物距离，并四舍五入保留两位小数
        obstacle_min_range = round(min(scan_range), 2)

        # 注释掉的代码，可用于计算最小障碍物的角度
        # obstacle_angle = np.argmin(scan_range)
        # 注释掉的代码，可用于计算目标点与机器人的 x 方向差距
        # x_gap  = self.goal_x - self.position.x
        # 注释掉的代码，可用于计算目标点与机器人的 y 方向差距
        # y_gap = self.goal_y - self.position.y
        
        # 若最小障碍物距离小于最小安全距离且大于 0，认为发生碰撞，标记为结束
        if min_range > min(scan_range) > 0:
            print("scan_range",scan_range)
            print("min_range",min_range)
            print("min(scan_range)",min(scan_range))
            done = True

        # 计算机器人当前位置到目标点的距离，并四舍五入保留两位小数
        current_distance = round(math.hypot(self.goal_x - self.position.x, self.goal_y - self.position.y),2)
        # 若距离小于 0.3，认为到达目标点
        if current_distance < 0.3:
            self.get_goalbox = True

        # 注释掉的返回语句，可用于包含障碍物角度的状态返回
        # return scan_range + [heading, current_distance, obstacle_min_range, obstacle_angle], done
        # 返回包含激光雷达数据、朝向、当前距离、最小障碍物距离和上一次动作的状态列表，以及是否结束的标志
        return scan_range + [heading, current_distance, obstacle_min_range,past_action[0],past_action[1]], done
    
    def setReward(self, state, action,done):
        """
        根据当前状态、动作和是否结束的标志计算奖励。

        :param state: 当前环境状态
        :param action: 当前执行的动作
        :param done: 是否结束的标志
        :return: 计算得到的奖励和是否结束的标志
        """

        # 从状态中获取当前距离
        current_distance = state[-4]
        # 从状态中获取机器人朝向目标的角度
        heading = state[-5]
        # 从状态中获取最小障碍物距离
        obstacle_min_range = state[-3]

        # 距离奖励，距离越远奖励越低
        distance_reward = -current_distance
        
        # 方向奖励，朝向偏差越大奖励越低
        turn_reward = -abs(heading)

        # 躲避障碍物体 Reward，若最小障碍物距离小于 0.8，给予负奖励
        if obstacle_min_range < 0.8:
            ob_reward = -2 ** (0.6/obstacle_min_range)
        else:
            ob_reward = 0
        # 总奖励为距离奖励、方向奖励和避障奖励之和
        reward = distance_reward + turn_reward + ob_reward

        # 若发生碰撞，记录日志信息，给予 -200 的惩罚奖励，停止机器人运动，重置目标点索引
        if done:
            rospy.loginfo("Collision!!")
            reward = -200.
            self.pub_cmd_vel.publish(Twist())
            self.respawn_goal.index = 0

        # 若到达目标点，记录日志信息，给予 1600 的奖励，停止机器人运动，重置目标点位置和距离
        if self.get_goalbox:
            rospy.loginfo("Goal!!")
            reward = 1600.
            self.pub_cmd_vel.publish(Twist())
            self.goal_x, self.goal_y = self.respawn_goal.getPosition(True, delete=True)
            self.goal_distance = self.getGoalDistace()
            self.get_goalbox = False
            

        return reward, done
     
    def step(self, action,past_action):
        """
        执行一步动作，更新环境状态并计算奖励。

        :param action: 当前执行的动作
        :param past_action: 上一次执行的动作
        :return: 当前环境状态的 NumPy 数组、奖励和是否结束的标志
        """
        # 从动作中获取线速度
        linear_vel = action[0]
        # 从动作中获取角速度
        ang_vel = action[1]
        # 创建 Twist 消息对象，用于存储速度指令
        vel_cmd = Twist()
        # 设置线速度
        vel_cmd.linear.x = linear_vel
        # 设置角速度
        vel_cmd.angular.z = ang_vel
        # 发布速度指令
        self.pub_cmd_vel.publish(vel_cmd)

        # 初始化激光雷达数据变量
        data = None
        # 循环等待激光雷达数据，最多等待 5 秒
        while data is None:
            try:
                data = rospy.wait_for_message('limo/scan', LaserScan, timeout=5)
            except:
                pass

        # 获取当前环境状态和是否结束的标志
        state, done = self.getState(data, past_action)     
        # 计算奖励和是否结束的标志
        reward, done = self.setReward(state, action,done)
        
        # 将状态列表转换为 NumPy 数组并返回
        return np.asarray(state), reward, done

    
    def reset(self):
        """
        重置环境，包括重置仿真环境和初始化目标点。

        :return: 重置后的初始环境状态的 NumPy 数组
        """
        # 等待 '/gazebo/reset_world' 服务可用
        rospy.wait_for_service('gazebo/reset_world')
        try:
            # 调用服务重置仿真环境
            self.reset_proxy()
        except (rospy.ServiceException) as e:
            # 若服务调用失败，打印错误信息
            print("gazebo/reset_simulation service call failed")

        # 初始化激光雷达数据变量
        data = None
        
        # 循环等待激光雷达数据，最多等待 5 秒
        while data is None:
            try:
                data = rospy.wait_for_message('/limo/scan', LaserScan, timeout=5)
            except:
                pass
        
        # 若需要初始化目标点，调用 Respawn 类的方法获取目标点位置，并标记为已初始化
        if self.initGoal:
            self.goal_x, self.goal_y = self.respawn_goal.getPosition()
            self.initGoal = False
            
        ## mabe debug
        # else:
        #     self.goal_x, self.goal_y = self.respawn_goal.getPosition(True, delete=True)
           

        # 打印重置成功信息
        print("reset successfully")
        # 计算初始目标距离
        self.goal_distance = self.getGoalDistace()
        # 获取初始环境状态
        state, _ = self.getState(data, [0]*self.action_dim)
        # 将状态列表转换为 NumPy 数组并返回
        return np.asarray(state)
    
