# 指定文件编码为 UTF-8
# coding: UTF-8
# 导入 sys 模块，用于与 Python 解释器进行交互
import sys
# 导入 os 模块，用于与操作系统进行交互
import os
# 将当前文件所在目录的上一级目录添加到系统路径中，方便后续导入模块
sys.path.append(os.path.join(os.path.dirname(__file__) + '/../')) # Add the path

# 导入 numpy 库，用于数值计算
import numpy as np
# 导入 PyTorch 库，用于深度学习任务
import torch
# 导入 Python 内置的 random 模块，用于生成随机数
import random

# 导入自定义的固定随机种子的模块
import fixed_seed # the module that fixes the seed 

# 导入 OpenAI Gym 库，用于强化学习环境的搭建
import gym 
# 设置 Gym 日志级别为 40，隐藏警告日志
gym.logger.set_level(40) # Hide warning log

# 导入自定义的 Gym 环境模块
import gym_pathplan # import Self-made module

# 导入训练器模块
import trainer
# 导入基于拉格朗日乘数法的 TD3 算法模块
import lagrangian_td3

def main():
    # 定义要使用的 Gym 环境 ID
    ENV_ID = 'GFPathPlan-v0'

    # 设置随机种子，可取值 0, 1, 2, ..., 9
    SEED = 9 # set SEED 0, 1, 2, ..., 9
    
    # 定义训练的总步数
    NUM_STEPS = 6 * 10 ** 5
    # 定义评估的间隔步数
    EVAL_INTERVAL = 10 ** 4
    # 定义每次评估的回合数
    NUM_EVAL_EPISODES = 100

    # 创建训练环境
    env = gym.make(ENV_ID)
    # 创建测试环境
    env_test = gym.make(ENV_ID)

    # 调用自定义模块中的函数，设置固定的随机种子
    fixed_seed.fixed_seed_function(SEED)
    
    # 为训练环境设置随机种子
    env.seed(SEED)
    env.action_space.seed(SEED)
    env.observation_space.seed(SEED)
    # 为测试环境设置随机种子，使用 2**31 - SEED 以保证与训练环境种子不同
    env_test.seed(2**31-SEED)
    env_test.action_space.seed(2**31-SEED)
    env_test.observation_space.seed(2**31-SEED)

    # 打印原始系统状态的形状
    print(env.observation_space.shape) # Original system state
    # 打印预处理后状态的形状
    print(env.extended_state_space.shape) # Pre-processed state

    # 定义训练时的批次大小
    BATCH_SIZE = 64
    # 定义折扣因子
    GAMMA = 0.99
    # 定义演员网络的学习率
    LEARNING_RATE_ACTOR = 3e-4
    # 定义评论家网络的学习率
    LEARNING_RATE_CRITIC = 3e-4
    # 定义拉格朗日乘数的学习率
    LEARNING_RATE_KAPPA = 1e-5
    # 定义重放缓冲区的大小
    REPLAY_BUFFER_SIZE = 10**5
    # 定义目标网络软更新的系数
    TAU = 0.01 
    # 定义 TD3 算法中的策略噪声参数
    POLICY_NOISE = 0.2 # TD3 hyper param
    # 定义 TD3 算法中的噪声裁剪范围
    NOISE_CLIP = 0.5 # TD3 hyper param
    # 定义 TD3 算法中策略更新的频率
    POLICY_FREQ = 2 # TD3 hyper param
    # 定义奖励的缩放因子
    REWARD_SCALE = 1.0

    # 定义所提出方法的阈值参数
    THRESHOLD = -40.
    # 定义预训练的步数
    PRETRAIN_STEPS = 300000
    #PRETRAIN_STEPS = 0

    # 初始化基于拉格朗日乘数法的 TD3 算法实例
    algo = lagrangian_td3.LagrangianTD3(
        state_shape=env.extended_state_space.shape,
        action_shape=env.action_space.shape,
        seed=SEED,
        batch_size=BATCH_SIZE,
        gamma=GAMMA,
        lr_actor=LEARNING_RATE_ACTOR,
        lr_critic=LEARNING_RATE_CRITIC,
        lr_kappa=LEARNING_RATE_KAPPA,
        threshold = THRESHOLD,
        pretrain_steps = PRETRAIN_STEPS, 
        replay_size=REPLAY_BUFFER_SIZE,
        tau=TAU,
        policy_noise=POLICY_NOISE,
        noise_clip=NOISE_CLIP,
        policy_freq=POLICY_FREQ,
        reward_scale=REWARD_SCALE,
    )

    # 初始化训练器实例
    Lagrangian_TD3_trainer = trainer.Trainer(
        env=env,
        env_test=env_test,
        algo=algo,
        seed=SEED,
        num_steps=NUM_STEPS,
        eval_interval=EVAL_INTERVAL,
        num_eval_episodes=NUM_EVAL_EPISODES,
    )

    # 开始训练
    Lagrangian_TD3_trainer.train() # training
    # 保存训练结果
    Lagrangian_TD3_trainer.save_result() # saving result
    
    # 关闭训练环境
    env.close()
    # 关闭测试环境
    env_test.close()

if __name__ == "__main__":
    # 当脚本作为主程序运行时，调用 main 函数
    main()
