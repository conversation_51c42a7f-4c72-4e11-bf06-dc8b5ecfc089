<?xml version="1.0"?>
<package>
  <name>limo_gazebo_sim</name>
  <version>0.0.1</version>
  <description>The limo_gazebo_sim package</description>

  <maintainer email="<EMAIL>">agilex</maintainer>
  <license>BSD 3-Clause</license>

  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>roslaunch</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>sensor_msgs</run_depend>
  <run_depend>controller_manager</run_depend>
  <run_depend>gazebo_plugins</run_depend>
  <run_depend>gazebo_ros</run_depend>
  <run_depend>gazebo_ros_control</run_depend>
  <run_depend>gazebo_dev</run_depend>
  <run_depend>gazebo_msgs</run_depend>    
  <run_depend>limo_description</run_depend>
  <run_depend>rostopic</run_depend>
</package>
