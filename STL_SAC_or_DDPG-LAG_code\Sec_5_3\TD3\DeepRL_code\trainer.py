# 从 abc 模块导入 ABC 和 abstractmethod，用于定义抽象基类和抽象方法
from abc import ABC, abstractmethod
# 导入 matplotlib.pyplot 模块，用于绘图
import matplotlib.pyplot as plt
# 从 matplotlib 模块导入 animation 子模块，用于创建动画
from matplotlib import animation
# 从 time 模块导入 time 函数，用于获取当前时间戳
from time import time
# 从 datetime 模块导入 timedelta 类，用于处理时间间隔
from datetime import timedelta
# 导入 numpy 库，用于数值计算
import numpy as np
# 导入 PyTorch 库，用于深度学习任务
import torch
# 导入 pandas 库，用于数据处理和分析
import pandas as pd

class Trainer:
    """
    训练器类，负责管理强化学习算法的训练流程，包括数据收集、模型更新和评估。
    """

    def __init__(self, env, env_test, algo, seed=0, num_steps=6*10**5, eval_interval=10**4, num_eval_episodes=100):
        """
        初始化训练器实例。

        参数:
        env (object): 训练环境对象。
        env_test (object): 测试环境对象。
        algo (object): 强化学习算法实例。
        seed (int, 可选): 随机种子，默认为 0。
        num_steps (int, 可选): 总的训练步数，默认为 600000。
        eval_interval (int, 可选): 评估的间隔步数，默认为 10000。
        num_eval_episodes (int, 可选): 每次评估的回合数，默认为 100。
        """
        # 训练环境
        self.env = env
        # 测试环境
        self.env_test = env_test
        # 强化学习算法实例
        self.algo = algo

        # 用于存储训练结果的字典，包含步数、奖励、STL 奖励、成功率和拉格朗日乘数
        self.returns = {'step': [], 'return': [], 'stl_return': [], 'success_rate':[], 'kappa':[]}

        # 总的训练步数
        self.num_steps = num_steps
        # 评估的间隔步数
        self.eval_interval = eval_interval
        # 每次评估的回合数
        self.num_eval_episodes = num_eval_episodes

    def train(self):
        """
        重复执行经验数据收集、模型学习和评估操作，直到达到指定的训练步数。
        """

        # 将从初始状态分布采样得到的状态数据存入初始状态缓冲区
        for i in range(2000):
            # 重置训练环境，获取初始状态
            init_state = self.env.reset()
            # 将初始状态存入算法的初始状态缓冲区
            self.algo.init_state_stock(init_state)
        #print("Num of Data in Init State Buffer "+str(self.algo.init_state_buffer._n))

        # 记录训练开始时间
        self.start_time = time()

        # 记录当前回合的步数
        t = 0

        # 初始化训练环境，获取初始状态
        state = self.env.reset()

        # 初始化处理时间变量
        self.processing_time = 0.0

        # 从 1 开始迭代，直到达到总的训练步数
        for steps in range(1, self.num_steps + 1):

            # 记录处理前的时间戳
            before_processing = time() #########################
            
            # 算法执行一步探索操作，更新状态和回合步数
            state, t = self.algo.step(self.env, state, t, steps) # exploration
            
            # 判断是否满足更新条件，若满足则进行模型更新
            if self.algo.is_update(steps):
                self.algo.update(steps)
            # 记录处理后的时间戳
            after_processing = time() ##########################
            # 累加处理时间
            self.processing_time += after_processing - before_processing

            # 每隔指定的间隔步数进行一次评估
            if steps % self.eval_interval == 0: # 10000 steps
                self.evaluate(steps)

        # 打印总的处理时间
        print(f'Processing Time: {str(timedelta(seconds=int(self.processing_time)))}')
        #self.save_gif()
    
    #def save_gif(self):
    #    images = []
    #    state = self.env_test.reset()
    #    done = False

    #    while(not done):
    #        images.append(self.env_test.render(mode='rgb_array'))
    #        action = self.algo.exploit(state)
    #        state, reward, stl_reward, done, _ = self.env_test.step(action)
    #    self.display_video(images) 

    #def display_video(self, frames):
    #    #plt.figure(figsize=(frames[0].shape[1]/72.0, frames[0].shape[0]/72.0), dpi=72)
    #    plt.figure(figsize=(8, 8), dpi=50)
    #    patch = plt.imshow(frames[0])
    #    plt.axis('off')

    #    def animate(i):
    #        patch.set_data(frames[i])

    #    anim = animation.FuncAnimation(plt.gcf(), animate, frames=len(frames), interval=50)
    #    anim.save('env.gif', writer='PillowWriter')  

    def evaluate(self, steps):
        """
        在测试环境中评估当前算法的性能。

        参数:
        steps (int): 当前的训练步数。
        """

        # 存储每个回合的折扣奖励总和
        returns = [] 
        # 存储每个回合的折扣 STL 奖励总和
        stl_returns = [] 
        # 存储每个回合的评估值，用于计算成功率
        evaluates = [] 
        # 折扣因子
        GAMMA = 0.99 

        # 进行指定回合数的评估
        for _ in range(self.num_eval_episodes): # 100
            # 初始化评估值
            evaluate_val = 1.0
            # 重置测试环境，获取初始状态
            state = self.env_test.reset()
            # 计算初始的 STL 公式评估值
            eval_temp = self.env_test.evaluate_stl_formula()
            # 更新评估值，取最小值
            evaluate_val = min(evaluate_val, eval_temp) # \Phi = G\phi
            # 标记回合是否结束
            done = False
            # 初始化当前回合的折扣奖励总和
            episode_return = 0.0
            # 初始化当前回合的折扣 STL 奖励总和
            episode_stl_return = 0.0
            # 折扣因子的指数计数器
            gamma_count = 0

            # 当回合未结束时，继续执行动作
            while (not done):
                #self.env_test.render() 
                # 算法根据当前状态选择最优动作
                action = self.algo.exploit(state)
                # 在测试环境中执行动作，获取下一个状态、奖励、STL 奖励、结束标志等信息
                state, reward, stl_reward, done, _ = self.env_test.step(action)
                # 计算当前状态下的 STL 公式评估值
                eval_temp = self.env_test.evaluate_stl_formula()
                # 更新评估值，取最小值
                evaluate_val = min(evaluate_val, eval_temp) # \Phi = G\phi
                # 累加当前步骤的折扣奖励
                episode_return += (GAMMA**(gamma_count)) * reward
                # 累加当前步骤的折扣 STL 奖励
                episode_stl_return += (GAMMA**(gamma_count))  * stl_reward
                # 折扣因子指数加 1
                gamma_count += 1

            # 将当前回合的评估值添加到列表中
            evaluates.append(evaluate_val)
            # 将当前回合的折扣奖励总和添加到列表中
            returns.append(episode_return)
            # 将当前回合的折扣 STL 奖励总和添加到列表中
            stl_returns.append(episode_stl_return)

        # 将拉格朗日乘数从 GPU 移到 CPU 并转换为 NumPy 数组
        numpy_kappa = self.algo.kappa.cpu().detach().numpy() 

        # 计算平均折扣奖励
        mean_return = np.mean(returns)
        # 计算平均折扣 STL 奖励
        mean_stl_return = np.mean(stl_returns)
        # 计算成功率
        success_rate = np.mean(evaluates)
        # 将当前步数添加到结果字典中
        self.returns['step'].append(steps)
        # 将平均折扣奖励添加到结果字典中
        self.returns['return'].append(mean_return)
        # 将平均折扣 STL 奖励添加到结果字典中
        self.returns['stl_return'].append(mean_stl_return)
        # 将成功率添加到结果字典中
        self.returns['success_rate'].append(success_rate)
        # 将拉格朗日乘数添加到结果字典中
        self.returns['kappa'].append(numpy_kappa)

        # 打印当前步数、平均折扣奖励、平均折扣 STL 奖励、成功率、拉格朗日乘数和处理时间
        print(f'Num steps: {steps:<6}   '
              f'Return: {mean_return:<5.1f}   '
              f'STL Return: {mean_stl_return:<5.1f}   '
              f'Success Rate: {success_rate:<5.2f}   '
              f'Kappa: {numpy_kappa:<5.4f}   '
              f'Time: {str(timedelta(seconds=int(self.processing_time)))}') # self.time -> self.processing_time
        # 每隔 10000 步备份一次模型
        if steps % 10000 == 0:    
            self.algo.backup_model(steps)

    def save_result(self):
        """
        将训练结果保存到 CSV 文件中。
        """

        # 将折扣奖励结果转换为 DataFrame 并保存到 rewards.csv 文件中
        datasets = pd.DataFrame(self.returns['return'])
        datasets.to_csv('rewards.csv', mode='w')
        # 将折扣 STL 奖励结果转换为 DataFrame 并保存到 stl_rewards.csv 文件中
        datasets = pd.DataFrame(self.returns['stl_return'])
        datasets.to_csv('stl_rewards.csv', mode='w')
        # 将成功率结果转换为 DataFrame 并保存到 success.csv 文件中
        datasets = pd.DataFrame(self.returns['success_rate'])
        datasets.to_csv('success.csv', mode='w')
        # 将拉格朗日乘数结果转换为 DataFrame 并保存到 kappa.csv 文件中
        datasets = pd.DataFrame(self.returns['kappa'])
        datasets.to_csv('kappa.csv', mode='w')

    @property
    def time(self):
        """
        获取从训练开始到现在的时间间隔。

        返回:
        str: 时间间隔的字符串表示。
        """
        return str(timedelta(seconds=int(time() - self.start_time)))

class Algorithm(ABC):
    """
    抽象基类，定义了强化学习算法需要实现的抽象方法。
    """

    @abstractmethod
    def explore(self, state):
        """
        在探索模式下根据当前状态选择动作。

        参数:
        state (object): 当前状态。

        返回:
        object: 选择的动作。
        """
        pass

    @abstractmethod
    def exploit(self, state):
        """
        在利用模式下根据当前状态选择最优动作。

        参数:
        state (object): 当前状态。

        返回:
        object: 选择的最优动作。
        """
        pass

    @abstractmethod
    def is_update(self, steps):
        """
        判断是否满足模型更新的条件。

        参数:
        steps (int): 当前的训练步数。

        返回:
        bool: 是否满足更新条件。
        """
        pass

    @abstractmethod
    def step(self, env, state, t, steps):
        """
        执行一步探索操作，与环境进行交互。

        参数:
        env (object): 环境对象。
        state (object): 当前状态。
        t (int): 当前回合的步数。
        steps (int): 当前的训练步数。

        返回:
        tuple: 包含下一个状态和更新后的回合步数的元组。
        """
        pass

    @abstractmethod
    def update(self, steps):
        """
        根据当前的训练步数更新模型参数。

        参数:
        steps (int): 当前的训练步数。
        """
        pass

    @abstractmethod
    def backup_model(self, steps):
        """
        根据当前的训练步数备份模型。

        参数:
        steps (int): 当前的训练步数。
        """
        pass
