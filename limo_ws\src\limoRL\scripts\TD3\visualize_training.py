#!/usr/bin/env python3

import matplotlib.pyplot as plt
import numpy as np
import pickle
import os
import argparse

def plot_training_results(log_file):
    """
    可视化训练结果
    """
    try:
        with open(log_file, 'rb') as f:
            data = pickle.load(f)
    except FileNotFoundError:
        print(f"找不到日志文件: {log_file}")
        return
    except Exception as e:
        print(f"读取日志文件失败: {e}")
        return
    
    episodes = data.get('episodes', [])
    nav_rewards = data.get('nav_rewards', [])
    stl_rewards = data.get('stl_rewards', [])
    episode_lengths = data.get('episode_lengths', [])
    kappa_values = data.get('kappa_values', [])
    phases = data.get('phases', [])
    
    if not episodes:
        print("日志文件中没有数据")
        return
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Lagrangian TD3 Training Results', fontsize=16)
    
    # 1. 奖励曲线
    ax1 = axes[0, 0]
    ax1.plot(episodes, nav_rewards, label='Navigation Reward', alpha=0.7)
    ax1.plot(episodes, stl_rewards, label='STL Reward', alpha=0.7)
    
    # 添加移动平均
    if len(nav_rewards) > 50:
        window = 50
        nav_ma = np.convolve(nav_rewards, np.ones(window)/window, mode='valid')
        stl_ma = np.convolve(stl_rewards, np.ones(window)/window, mode='valid')
        ax1.plot(episodes[window-1:], nav_ma, '--', label='Nav MA(50)', linewidth=2)
        ax1.plot(episodes[window-1:], stl_ma, '--', label='STL MA(50)', linewidth=2)
    
    ax1.set_xlabel('Episode')
    ax1.set_ylabel('Reward')
    ax1.set_title('Training Rewards')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 回合长度
    ax2 = axes[0, 1]
    ax2.plot(episodes, episode_lengths, alpha=0.7, color='green')
    
    if len(episode_lengths) > 50:
        length_ma = np.convolve(episode_lengths, np.ones(50)/50, mode='valid')
        ax2.plot(episodes[49:], length_ma, '--', linewidth=2, color='darkgreen', label='MA(50)')
        ax2.legend()
    
    ax2.set_xlabel('Episode')
    ax2.set_ylabel('Episode Length')
    ax2.set_title('Episode Length')
    ax2.grid(True, alpha=0.3)
    
    # 3. 拉格朗日乘数
    ax3 = axes[1, 0]
    if kappa_values:
        ax3.plot(episodes, kappa_values, color='red', alpha=0.7)
        ax3.set_xlabel('Episode')
        ax3.set_ylabel('Kappa Value')
        ax3.set_title('Lagrangian Multiplier (κ)')
        ax3.grid(True, alpha=0.3)
        ax3.set_yscale('log')  # 对数坐标
    else:
        ax3.text(0.5, 0.5, 'No Kappa Data', ha='center', va='center', transform=ax3.transAxes)
        ax3.set_title('Lagrangian Multiplier (κ)')
    
    # 4. 训练阶段
    ax4 = axes[1, 1]
    if phases:
        pretrain_episodes = [e for e, p in zip(episodes, phases) if p == 'pretrain']
        finetune_episodes = [e for e, p in zip(episodes, phases) if p == 'finetune']
        
        if pretrain_episodes:
            ax4.scatter(pretrain_episodes, [1]*len(pretrain_episodes), 
                       label='Pretrain', alpha=0.6, s=10)
        if finetune_episodes:
            ax4.scatter(finetune_episodes, [2]*len(finetune_episodes), 
                       label='Finetune', alpha=0.6, s=10)
        
        ax4.set_xlabel('Episode')
        ax4.set_ylabel('Training Phase')
        ax4.set_title('Training Phase')
        ax4.set_yticks([1, 2])
        ax4.set_yticklabels(['Pretrain', 'Finetune'])
        ax4.legend()
        ax4.grid(True, alpha=0.3)
    else:
        ax4.text(0.5, 0.5, 'No Phase Data', ha='center', va='center', transform=ax4.transAxes)
        ax4.set_title('Training Phase')
    
    plt.tight_layout()
    plt.show()

def plot_test_results(test_file):
    """
    可视化测试结果
    """
    try:
        with open(test_file, 'rb') as f:
            data = pickle.load(f)
    except FileNotFoundError:
        print(f"找不到测试文件: {test_file}")
        return
    except Exception as e:
        print(f"读取测试文件失败: {e}")
        return
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Test Results Analysis', fontsize=16)
    
    # 1. 成功率饼图
    ax1 = axes[0, 0]
    success_rate = data.get('success_rate', 0)
    collision_rate = data.get('collision_rate', 0)
    timeout_rate = data.get('timeout_rate', 0)
    
    labels = ['Success', 'Collision', 'Timeout']
    sizes = [success_rate, collision_rate, timeout_rate]
    colors = ['green', 'red', 'orange']
    
    ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title('Test Results Distribution')
    
    # 2. 奖励分布
    ax2 = axes[0, 1]
    nav_rewards = data.get('nav_rewards', [])
    stl_rewards = data.get('stl_rewards', [])
    
    if nav_rewards and stl_rewards:
        ax2.hist(nav_rewards, alpha=0.7, label='Navigation Rewards', bins=20)
        ax2.hist(stl_rewards, alpha=0.7, label='STL Rewards', bins=20)
        ax2.set_xlabel('Reward Value')
        ax2.set_ylabel('Frequency')
        ax2.set_title('Reward Distribution')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    # 3. 回合长度分布
    ax3 = axes[1, 0]
    episode_lengths = data.get('episode_lengths', [])
    
    if episode_lengths:
        ax3.hist(episode_lengths, bins=20, alpha=0.7, color='purple')
        ax3.set_xlabel('Episode Length')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Episode Length Distribution')
        ax3.grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_length = np.mean(episode_lengths)
        std_length = np.std(episode_lengths)
        ax3.axvline(mean_length, color='red', linestyle='--', 
                   label=f'Mean: {mean_length:.1f}±{std_length:.1f}')
        ax3.legend()
    
    # 4. 性能指标总结
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    # 创建性能指标文本
    metrics_text = f"""
    Test Performance Summary:
    
    Success Rate: {success_rate*100:.1f}%
    Collision Rate: {collision_rate*100:.1f}%
    Timeout Rate: {timeout_rate*100:.1f}%
    
    Average Episode Length: {data.get('avg_episode_length', 0):.1f}
    Average Nav Reward: {data.get('avg_nav_reward', 0):.2f}
    Average STL Reward: {data.get('avg_stl_reward', 0):.2f}
    
    Total Test Episodes: {len(episode_lengths)}
    """
    
    ax4.text(0.1, 0.9, metrics_text, transform=ax4.transAxes, fontsize=12,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

def main():
    parser = argparse.ArgumentParser(description='可视化训练和测试结果')
    parser.add_argument('--type', choices=['train', 'test'], required=True,
                       help='可视化类型: train (训练结果) 或 test (测试结果)')
    parser.add_argument('--file', required=True,
                       help='数据文件路径')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.file):
        print(f"文件不存在: {args.file}")
        return
    
    if args.type == 'train':
        plot_training_results(args.file)
    elif args.type == 'test':
        plot_test_results(args.file)

if __name__ == '__main__':
    # 如果没有命令行参数，提供交互式选择
    import sys
    if len(sys.argv) == 1:
        print("可视化工具")
        print("1. 训练结果可视化")
        print("2. 测试结果可视化")
        
        choice = input("请选择 (1/2): ")
        
        if choice == '1':
            file_path = input("请输入训练日志文件路径: ")
            if os.path.exists(file_path):
                plot_training_results(file_path)
            else:
                print("文件不存在")
        elif choice == '2':
            file_path = input("请输入测试结果文件路径: ")
            if os.path.exists(file_path):
                plot_test_results(file_path)
            else:
                print("文件不存在")
        else:
            print("无效选择")
    else:
        main()
