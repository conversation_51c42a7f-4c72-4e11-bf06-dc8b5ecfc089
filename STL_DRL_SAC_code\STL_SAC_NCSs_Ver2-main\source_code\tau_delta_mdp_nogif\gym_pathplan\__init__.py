# 从 gym 库的环境注册模块导入 register 函数，用于向 Gym 环境注册表中注册自定义环境
from gym.envs.registration import register

# 注册一个名为 'STLPathPlan-v0' 的 Gym 环境
register(
    # 环境的唯一标识符，在创建环境时使用该 ID
    id='STLPathPlan-v0',
    # 指定环境类的入口点，这里表示环境类 STL_Problem 位于 gym_pathplan.envs 模块中
    entry_point='gym_pathplan.envs:STL_Problem',
)
# 注册一个名为 'STLPathPlan-v1' 的 Gym 环境
register(
    # 环境的唯一标识符，在创建环境时使用该 ID
    id='STLPathPlan-v1',
    # 指定环境类的入口点，这里表示环境类 STL_Problem_Preprocess 位于 gym_pathplan.envs 模块中
    entry_point='gym_pathplan.envs:STL_Problem_Preprocess',
)
