# 实物Limo机器人STL任务部署指南

## 概述

本指南详细说明如何将训练好的STL-TD3模型部署到真实的Limo机器人上，完成STL（Signal Temporal Logic）任务。

## 系统架构

```
实物Limo机器人
├── 硬件层
│   ├── 激光雷达 (LiDAR)
│   ├── 里程计 (Odometry)
│   ├── 电机控制器
│   └── 主控计算机
├── ROS通信层
│   ├── 传感器数据订阅
│   ├── 控制命令发布
│   └── 状态信息发布
├── 定位导航层
│   ├── AMCL定位 (可选)
│   ├── 地图服务器
│   └── TF坐标变换
└── STL控制层
    ├── 环境状态获取
    ├── STL约束计算
    ├── 模型推理
    └── 动作执行
```

## 前置条件

### 1. 硬件要求
- Limo机器人（配备激光雷达）
- 主控计算机（推荐：Jetson Nano/Xavier或笔记本电脑）
- 稳定的电源供应

### 2. 软件要求
- Ubuntu 18.04/20.04
- ROS Melodic/Noetic
- Python 3.6+
- PyTorch 1.8+
- 必要的Python包：numpy, rospy, tf2_ros

### 3. 预训练模型
- 完成训练的STL-TD3模型文件
- 模型应包含：actor网络、critic网络、Lagrangian参数

## 部署步骤

### 步骤1: 环境准备

#### 1.1 安装依赖
```bash
# 安装Python依赖
pip3 install torch numpy scipy matplotlib

# 安装ROS包（如果未安装）
sudo apt-get install ros-noetic-navigation
sudo apt-get install ros-noetic-map-server
sudo apt-get install ros-noetic-amcl
```

#### 1.2 配置工作空间
```bash
# 进入工作空间
cd ~/limo_ws
source devel/setup.bash

# 确保脚本可执行
chmod +x src/limoRL/scripts/TD3/*.py
```

### 步骤2: 地图建立（如果使用AMCL定位）

#### 2.1 建图
```bash
# 启动Limo基础驱动
roslaunch limo_bringup limo_start.launch

# 启动SLAM建图
roslaunch limo_navigation limo_slam.launch

# 使用遥控器或键盘控制机器人移动，建立环境地图
# 保存地图
rosrun map_server map_saver -f ~/limo_ws/src/limo_navigation/maps/my_map
```

#### 2.2 测试定位
```bash
# 启动导航系统
roslaunch limo_navigation limo_navigation.launch

# 在rviz中设置初始位置，确认定位正常
```

### 步骤3: STL区域标定

#### 3.1 启动标定工具
```bash
# 方法1: 使用部署脚本
python3 deploy_real_limo.py --mode calibration

# 方法2: 手动启动
python3 stl_region_calibrator.py
```

#### 3.2 标定STL区域
```bash
# 开始定义第一个区域
rostopic pub /calibration_command std_msgs/String "data: 'start_region'"

# 在rviz中使用"2D Nav Goal"工具点击区域边界点
# 完成区域定义
rostopic pub /calibration_command std_msgs/String "data: 'finish_region'"

# 设置区域名称和类型
rostopic pub /calibration_command std_msgs/String "data: 'set_region_name:target_area'"
rostopic pub /calibration_command std_msgs/String "data: 'set_region_type:visit'"

# 重复以上步骤定义更多区域
# 保存配置
rostopic pub /calibration_command std_msgs/String "data: 'save_config'"
```

### 步骤4: 部署STL控制系统

#### 4.1 完整系统部署
```bash
# 使用部署脚本（推荐）
python3 deploy_real_limo.py --mode full --model /path/to/your/model.pth --task basic_visit

# 手动部署
python3 real_limo_controller.py --model /path/to/your/model.pth --task basic_visit --amcl
```

#### 4.2 系统启动检查
确认以下节点正常运行：
- `/roscore`
- `/map_server`
- `/amcl` (如果使用AMCL)
- `/real_limo_stl_controller`
- `/stl_region_calibrator` (如果需要)

### 步骤5: 执行STL任务

#### 5.1 设置初始位置
在rviz中使用"2D Pose Estimate"工具设置机器人初始位置

#### 5.2 启动任务
```bash
# 开始STL任务
rostopic pub /limo_start_stop std_msgs/Bool "data: true"

# 停止任务
rostopic pub /limo_start_stop std_msgs/Bool "data: false"
```

#### 5.3 监控任务状态
```bash
# 监控任务状态
rostopic echo /stl_task_status

# 监控控制状态
rostopic echo /limo_control_status

# 监控性能统计
rostopic echo /limo_performance
```

## STL约束修改

### 1. 修改预定义任务

编辑 `stl_config.py` 文件中的 `PREDEFINED_TASKS` 字典：

```python
"custom_task": {
    "description": "自定义STL任务",
    "regions": [
        STLRegion("region1", (1.0, 2.0), (1.0, 2.0), "visit"),
        STLRegion("danger", (3.0, 4.0), (3.0, 4.0), "avoid")
    ],
    "formula": STLFormula("custom", "logical", [], operator="and"),
    "tau": 120,
    "beta": 1.5,
    "threshold": -35.0
}
```

### 2. 动态加载任务

```bash
# 切换到不同的预定义任务
rostopic pub /stl_task_update std_msgs/String '{"task_name": "patrol"}'

# 从文件加载自定义任务
rostopic pub /stl_task_update std_msgs/String '{"config_file": "/path/to/custom_task.json"}'
```

### 3. 创建自定义STL公式

```python
# 示例：顺序访问任务
regions = [
    STLRegion("start", (0.0, 1.0), (0.0, 1.0), "visit"),
    STLRegion("checkpoint1", (2.0, 3.0), (2.0, 3.0), "visit"),
    STLRegion("checkpoint2", (4.0, 5.0), (4.0, 5.0), "visit"),
    STLRegion("end", (6.0, 7.0), (6.0, 7.0), "visit")
]

formula = STLFormula("sequential_visit", "temporal", regions, operator="until")
custom_task = STLConfig.create_custom_task(regions, formula, tau=200, beta=2.0)
```

## 故障排除

### 常见问题

#### 1. 模型加载失败
- 检查模型文件路径是否正确
- 确认模型参数与训练时一致
- 检查PyTorch版本兼容性

#### 2. 传感器数据异常
- 检查激光雷达连接
- 确认ROS话题发布正常：`rostopic list | grep scan`
- 检查数据格式：`rostopic echo /scan`

#### 3. 定位不准确
- 重新设置初始位置
- 检查地图质量
- 调整AMCL参数

#### 4. 控制响应异常
- 检查控制频率设置
- 确认安全限制参数
- 监控碰撞检测状态

### 调试命令

```bash
# 检查ROS节点状态
rosnode list
rosnode info /real_limo_stl_controller

# 检查话题通信
rostopic list
rostopic hz /cmd_vel
rostopic echo /stl_task_status

# 查看TF树
rosrun tf2_tools view_frames.py
evince frames.pdf

# 检查参数服务器
rosparam list
rosparam get /amcl
```

## 性能优化

### 1. 控制频率调整
根据计算能力调整控制频率：
```bash
python3 real_limo_controller.py --freq 5.0  # 降低到5Hz
```

### 2. 模型推理优化
- 使用GPU加速（如果可用）
- 模型量化减少计算量
- 批处理推理

### 3. 传感器数据处理
- 激光雷达数据降采样
- 滤波减少噪声
- 异步数据处理

## 安全注意事项

### 1. 紧急停止
系统提供多种紧急停止方式：
```bash
# ROS话题停止
rostopic pub /emergency_stop std_msgs/Bool "data: true"

# 控制器停止
rostopic pub /limo_start_stop std_msgs/Bool "data: false"

# 物理急停按钮（如果有）
```

### 2. 安全限制
- 最大线速度限制：0.5 m/s
- 最大角速度限制：1.0 rad/s
- 碰撞检测阈值：0.3 m
- 自动停止机制

### 3. 监控建议
- 始终监控机器人状态
- 准备手动接管控制
- 确保工作区域安全

## 扩展功能

### 1. 多机器人协作
- 修改命名空间支持多机器人
- 实现分布式STL任务分配
- 协调避碰机制

### 2. 动态环境适应
- 实时障碍物检测
- 动态路径重规划
- 自适应STL约束调整

### 3. 人机交互
- 语音命令接口
- 手势识别控制
- 移动端监控应用

## 总结

通过本指南，您可以成功将STL-TD3模型部署到实物Limo机器人上。关键步骤包括：

1. **环境准备**：确保硬件和软件环境正确配置
2. **地图建立**：为定位系统提供准确的环境地图
3. **区域标定**：精确定义STL任务区域
4. **系统部署**：启动完整的控制系统
5. **任务执行**：监控和管理STL任务执行

部署成功后，机器人将能够在真实环境中完成复杂的STL时序逻辑任务，同时保持安全的障碍物避让能力。
