#!/usr/bin/python3
import rospy
import numpy as np
import math
from math import pi
from geometry_msgs.msg import Twist, Point, Pose
from sensor_msgs.msg import LaserScan
from nav_msgs.msg import Odometry
from std_srvs.srv import Empty
from tf.transformations import euler_from_quaternion, quaternion_from_euler
from .respawnGoal import Respawn


class Env():
    def __init__(self,action_size):
        self.goal_x = 0
        self.goal_y = 0
        self.heading = 0
        self.action_size = action_size
        self.initGoal = True
        self.get_goalbox = False
        self.position = Pose()
        self.pub_cmd_vel = rospy.Publisher('cmd_vel', Twist, queue_size=5)
        self.sub_odom = rospy.Subscriber('odom', Odometry, self.getOdometry)
        self.reset_proxy = rospy.ServiceProxy('/gazebo/reset_world', Empty)
        self.unpause_proxy = rospy.ServiceProxy('gazebo/unpause_physics', Empty)
        self.pause_proxy = rospy.ServiceProxy('gazebo/pause_physics', Empty)
        self.respawn_goal = Respawn()
        self.last_distance = 0
        
        
    
    def getGoalDistace(self):
        goal_distance = round(math.hypot(self.goal_x - self.position.x, self.goal_y - self.position.y), 2)
        return goal_distance
    
    def getOdometry(self, odom):
        # 获得机器人的具体位置
        self.position = odom.pose.pose.position
        orientation = odom.pose.pose.orientation
        orientation_list = [orientation.x, orientation.y, orientation.z, orientation.w]
        _, _, yaw = euler_from_quaternion(orientation_list)
        
        goal_angle = math.atan2(self.goal_y - self.position.y, self.goal_x - self.position.x)

        heading = goal_angle - yaw

        if heading > pi:
            heading -= 2 * pi

        elif heading < -pi:
            heading += 2 * pi
        self.heading = round(heading, 2)
        
    def getState(self, scan,past_action):
        #state 20个激光雷达数据 + heading + current_disctance + obstacle_min_range, obstacle_angle 

        scan_range = []   
        heading = self.heading
        min_range = 0.20
        done = False

        for i in range(len(scan.ranges)):
            if scan.ranges[i] == float('Inf') or scan.ranges[i] >3.5:
                scan_range.append(3.5)
            elif np.isnan(scan.ranges[i]):
                scan_range.append(0)
            else:
                scan_range.append(scan.ranges[i])


        obstacle_min_range = round(min(scan_range), 2)

        # obstacle_angle = np.argmin(scan_range)
        if min_range > min(scan_range) > 0:
            done = True

        current_distance = round(math.hypot(self.goal_x - self.position.x, self.goal_y - self.position.y),2)
        if current_distance < 0.3:
            self.get_goalbox = True

        return scan_range + [heading, current_distance, obstacle_min_range,past_action], done
    
    def setReward(self, state, done, action):
        yaw_reward = []
        current_distance = state[-3]
        heading = state[-4]
        obstacle_min_range = state[-2]

        # # change reward
        # for i in range(5):
        #     angle = -pi / 4 + heading + (pi / 8 * i) + pi / 2
        #     tr = 1 - 4 * math.fabs(0.5 - math.modf(0.25 + 0.5 * angle % (2 * math.pi) / math.pi)[0])
        #     yaw_reward.append(tr)
            
        # if current_distance <= 0.8:   
        #     distance_rate = 6 ** (current_distance / self.goal_distance)
        # else:
        #     distance_rate = 2 ** (current_distance / self.goal_distance)
            

        # if obstacle_min_range < 0.3:
        #     ob_reward = -2
        # else:
        #     ob_reward = 0.25
        # # print("obstacle_min_range ",obstacle_min_range )
        # reward = ((round(yaw_reward[action] * 5, 2)) * distance_rate) + ob_reward

        # 距离奖励 
        distance_reward = -current_distance
        
        # 方向奖励
        turn_reward = -abs(heading)

        # 躲避障碍物体 Reward
        if obstacle_min_range < 0.8:
            ob_reward = -2 ** (0.6/obstacle_min_range)
        else:
            ob_reward = 0

        reward = distance_reward + turn_reward + ob_reward


        if done:
            rospy.loginfo("Collision!!")
            reward = -200
            self.pub_cmd_vel.publish(Twist())

        if self.get_goalbox:
            rospy.loginfo("Goal!!")
            reward = 1600
            self.pub_cmd_vel.publish(Twist())
            self.goal_x, self.goal_y = self.respawn_goal.getPosition(True, delete=True)
            self.goal_distance = self.getGoalDistace()
            self.get_goalbox = False

        return reward
    
    
    def Getang(self,past_action):
        max_angular_vel = 1.5
        ang_vel = ((self.action_size - 1)/2 - past_action) * max_angular_vel * 0.5
        return ang_vel

    def step(self, action,past_action):
        max_angular_vel = 1.5
        ang_vel = ((self.action_size - 1)/2 - action) * max_angular_vel * 0.5
        vel_cmd = Twist()
        vel_cmd.linear.x = 0.15
        vel_cmd.angular.z = ang_vel
        self.pub_cmd_vel.publish(vel_cmd)

        data = None
        while data is None:
            try:
                data = rospy.wait_for_message('/limo/scan', LaserScan, timeout=5)
            except:
                pass

        state, done = self.getState(data,past_action)
        reward = self.setReward(state, done, action)

        return np.asarray(state), reward, done
    
    
    def reset(self,past_action):
        rospy.wait_for_service('gazebo/reset_world')
        try:
            self.reset_proxy()
        except (rospy.ServiceException) as e:
            print("gazebo/reset_simulation service call failed")
        print("self.respawn_goal.check_model1 :",self.respawn_goal.check_model)
        data = None
        while data is None:
            try:
                data = rospy.wait_for_message('/limo/scan', LaserScan, timeout=5)
            except:
                pass
        print("self.respawn_goal.check_model2 :",self.respawn_goal.check_model)
        if self.initGoal:
     
            self.goal_x, self.goal_y = self.respawn_goal.getPosition()
            self.initGoal = False
           

        print("reset successfully")
        self.goal_distance = self.getGoalDistace()
        self.last_distance = self.goal_distance
        state, done = self.getState(data,past_action)

        return np.asarray(state)
    
        