# 导入 numpy 库，别名为 np，用于数值计算和数组操作
import numpy as np
# 导入 math 库，提供数学运算相关的函数
import math

# 导入 gym 库，用于创建和管理强化学习环境
import gym
# 从 gym 库中导入 error、spaces 和 utils 模块
# 注：当前代码中未实际使用 error 和 utils 模块，可考虑移除
from gym import error, spaces, utils
# 从 gym.utils 模块中导入 seeding 函数，用于生成随机数种子
from gym.utils import seeding

# 导入 sys 库，提供对 Python 解释器相关的变量和函数的访问
# 注：当前代码中未实际使用该库，可考虑移除
import sys
# 导入 os 库，提供与操作系统进行交互的功能
# 注：当前代码中未实际使用该库，可考虑移除
import os
# 注释表明当前暂时不导入渲染相关模块，后续可能会用到
#from gym.envs.classic_control import rendering 
##########################################################################################
# With Preprocess (dim(\hat{z})=25)
##########################################################################################
# 定义一个名为 STL_Problem_Preprocess 的类，继承自 gym.Env，用于创建一个带预处理的强化学习环境
class STL_Problem_Preprocess(gym.Env):
    # 定义环境的元数据，指定渲染模式支持 human 和 rgb_array 两种模式
    metadata = {'render.modes': ['human', 'rgb_array']}

    def __init__(self):
        # 渲染区域的范围，四个顶点坐标分别为 (0.0,0.0), (0.0,max_y), (max_x,0.0), (max_x,max_y)
        # 定义渲染窗口的最大 x 坐标值
        self._max_x_of_window = 5.0
        # 定义渲染窗口的最大 y 坐标值
        self._max_y_of_window = 5.0

        # 定义动态系统的采样周期，单位为秒
        self.dt = 0.1 # The sampling period of the dynamical system.
        # 定义一个回合的最大步数
        self._max_episode_steps = 1000

        # 子信号时序逻辑（subSTL）公式的时间范围
        hrz_phi = 99.
        # 定义第一个子 STL 公式的时间边界
        self.phi_1_timebound = [0.0, hrz_phi]
        # 定义第二个子 STL 公式的时间边界
        self.phi_2_timebound = [0.0, hrz_phi]
        # 计算时间范围加 1 的整数值，用于后续状态轨迹存储
        self.tau = int(hrz_phi + 1) # hrz(phi) + 1

        # 扩展状态中存储的过去动作的数量
        self.num_of_past_actions = 10

        # 真实延迟，对于智能体来说是不确定的值
        self.d_sc = 3 # not using
        self.d_ca = 4 # not using
        # 定义网络延迟
        self.network_delay = 7

        # log-sum-exp 近似参数，用于奖励计算
        self.beta = 100.

        # 机器人的参数，用于渲染
        self.robot_radius = 0.2 #[m]

        # 动作相关参数
        # 定义机器人的最大线速度，单位为米每秒
        self.max_velocity = 1.0   # [m/s]
        # 定义机器人的最小线速度，单位为米每秒
        self.min_velocity = -1.0  # [m/s]
        # 定义机器人的最大角速度，单位为弧度每秒
        self.max_angular_velocity = 1.0  # [rad/s]
        # 定义机器人的最小角速度，单位为弧度每秒
        self.min_angular_velocity = -1.0 # [rad/s]

        # 环境步数计数器，记录当前环境执行的步数
        self.num_steps = 0 # env_step counter

        # 状态空间的上下限，分别对应 [car_x, car_y, car_yaw]
        # 定义状态空间的上限
        self.high = np.array([np.inf, np.inf, np.pi], dtype=np.float32)
        # 定义状态空间的下限
        self.low = np.array([-np.inf, -np.inf, -np.pi], dtype=np.float32)
        # 定义机器人状态的维度
        self.car_dim = 3

        # 扩展状态空间的上下限
        # 定义扩展状态空间的下限，维度为 car_dim + 2 + num_of_past_actions*2
        self.low_extended_state_space = -np.ones(self.car_dim + 2 + self.num_of_past_actions*2) # 25  
        # 定义扩展状态空间的上限，维度为 car_dim + 2 + num_of_past_actions*2
        self.high_extended_state_space = np.ones(self.car_dim + 2 + self.num_of_past_actions*2) # 25

        # 定义动作空间 (线速度[m/s], 角速度[rad/s])
        # 定义动作空间的下限
        self.action_low  = np.array([self.min_velocity, self.min_angular_velocity]) 
        # 定义动作空间的上限
        self.action_high = np.array([self.max_velocity, self.max_angular_velocity]) 

        # 使用 gym 的 Box 空间定义动作空间，指定上下限和形状
        self.action_space = spaces.Box(low=self.action_low, high=self.action_high, shape =(2,), dtype=np.float32)
       
        # 使用 gym 的 Box 空间定义观测空间，指定上下限
        self.observation_space = spaces.Box(\
            low=self.low,\
            high=self.high,\
            dtype=np.float32 
        )

        # 使用 gym 的 Box 空间定义扩展状态空间，指定上下限
        self.extended_state_space = spaces.Box(\
            low=self.low_extended_state_space,\
            high=self.high_extended_state_space,\
            dtype=np.float32
        )

        # 初始化随机数种子
        self.seed()
        # 初始化渲染器，初始值为 None
        self.viewer = None
        # 初始化是否显示激光雷达的标志，设置为 True
        self.vis_lidar = True

        # 定义两个 STL 公式对应的区域范围
        # 初始状态区域的范围
        self.init_low_x = 0.0
        self.init_low_y = 0.0
        self.init_high_x = 2.5
        self.init_high_y = 2.5

        # 第一个 STL 公式对应的区域范围
        self.stl_1_low_x = 3.75
        self.stl_1_low_y = 3.75
        self.stl_1_high_x = 5.0
        self.stl_1_high_y = 5.0

        # 第二个 STL 公式对应的区域范围
        self.stl_2_low_x = 3.75
        self.stl_2_low_y = 1.25
        self.stl_2_high_x = 5.0
        self.stl_2_high_y = 2.5
        # ############################################################


    def reset(self): 
        """
        重置环境到初始状态，初始化系统状态、扩展状态和过去动作序列。

        :return: 初始的扩展状态观测值，即 z_0
        """
        # 初始化系统状态 x=[x0(米), x1(米), x2(弧度)]
        # 在初始区域范围内随机采样 x 坐标
        init_x0 = self.np_random.uniform(low=self.init_low_x, high=self.init_high_x)
        # 在初始区域范围内随机采样 y 坐标
        init_x1 = self.np_random.uniform(low=self.init_low_y, high=self.init_high_y)
        # 在 [-π/2, π/2] 范围内随机采样偏航角
        init_x2 = self.np_random.uniform(low=-np.pi/2, high=np.pi/2)

        # 初始化过去状态轨迹存储器 [x_{t-tau+1},x_{t-tau+2},...,x_{t}]
        self.past_state_trajectory = [] 

        # 初始化过去动作序列存储器 [a_{t-D},a_{t-D+1},...,a_{t-1}]
        self.past_action_list = [] 

        # 用初始状态填充过去状态轨迹，重复 tau 次，初始过去状态轨迹为 [x_0,x_0,...,x_0]
        for i in range(self.tau): 
            current_state = np.array([init_x0, init_x1, init_x2])
            self.past_state_trajectory.append(current_state)
        # 用零动作填充过去动作序列，重复 num_of_past_actions 次，初始过去动作序列为 [0,0,...,0]
        for i in range(self.num_of_past_actions): 
            temp_action = np.array([0.0,0.0]) # 0_{n_u}，表示零动作
            self.past_action_list.append(temp_action)

        # 重置探索步数计数器
        self.num_steps = 0 

        # 设置当前车辆状态
        self.state = np.array([init_x0, init_x1, init_x2]) 

        # 构建 tau-D 扩展状态，调用 observe 方法生成初始观测值 z_0
        self.observation = self.observe(self.past_state_trajectory, self.past_action_list) 
        
        # 重置回合结束标志
        self.done = False

        # 返回初始的扩展状态观测值 z_0 = [[x_0...x_0]^T [0 ... 0]^T]
        return self.observation 

    def step(self, action): 
        """
        执行环境的一个时间步，根据输入动作更新环境状态，并返回新的观测、奖励、是否结束标志和额外信息。

        :param action: 智能体执行的动作，是一个包含线速度和角速度的数组。
        :return: 元组，包含新的观测值、奖励值、是否结束标志和额外信息字典。
        """
        # k=0,1,2,..., 即 t = d_sc, d_sc+1, ...
        # 计算当前时间步的奖励，传入历史状态轨迹，奖励函数将状态空间 Z 映射到奖励空间 R
        reward = self.reward(self.past_state_trajectory) # R:Z -> R

        # 获取真实执行的动作，考虑网络延迟，从过去动作列表中取出 t - network_delay 时刻的动作
        true_action = self.past_action_list[len(self.past_action_list)-self.network_delay] # a_{t-7}

        # 更新过去动作列表，移除最早记录的动作
        self.past_action_list = self.past_action_list[1:] # Update of the past action list
        # 将当前输入的动作添加到过去动作列表的末尾
        self.past_action_list.append(action)  

        # 系统噪声，每个维度的噪声服从均值为 0，标准差为 0.1 的正态分布
        noise_w0 = 0.1*np.random.normal(0,1) 
        noise_w1 = 0.1*np.random.normal(0,1)
        noise_w2 = 0.1*np.random.normal(0,1)
        
        # 系统状态更新 t = 0,1,... ================================================
        # 更新状态的 x 坐标，考虑真实动作的线速度、当前偏航角和噪声
        self.state[0] += (true_action[0] * math.cos(self.state[2]) + noise_w0) * self.dt
        # 更新状态的 y 坐标，考虑真实动作的线速度、当前偏航角和噪声
        self.state[1] += (true_action[0] * math.sin(self.state[2]) + noise_w1) * self.dt
        # 更新状态的偏航角，考虑真实动作的角速度和噪声
        self.state[2] += (true_action[1] + noise_w2) * self.dt 

        # 确保偏航角在 [-π, π] 范围内
        if self.state[2] < -np.pi:
            self.state[2] += np.pi * 2.0
        elif math.pi < self.state[2]:
            self.state[2] -= np.pi * 2.0
        #======================================================================

        # 更新过去状态轨迹，移除最早记录的状态
        self.past_state_trajectory = self.past_state_trajectory[1:] # Update the past state trajectory
        # 将当前状态的副本添加到过去状态轨迹的末尾
        self.past_state_trajectory.append(self.state.copy())

        # 构建新的观测值，将更新后的过去状态轨迹和过去动作列表转换为扩展状态，返回给智能体
        self.observation = self.observe(self.past_state_trajectory,self.past_action_list) # return to the agent
        
        # 环境步数计数器加 1
        self.num_steps += 1

        # 判断当前回合是否达到最大步数
        if self.num_steps == self._max_episode_steps:
            return_done = True # Terminal of an episode
            # 回合结束，重置环境
            self.reset() 
        else:
            return_done = False

        # 返回新的观测值、奖励值、是否结束标志和额外信息字典
        return self.observation, reward, return_done, {}


    def observe(self, tau_state, D_actions): 
        """
        将过去的状态轨迹和动作序列包装成一个扩展状态，用于智能体观测。

        :param tau_state: 包含过去多个时刻状态的列表，每个状态是一个数组，代表机器人在某一时刻的状态。
        :param D_actions: 包含过去多个时刻动作的列表，每个动作是一个包含线速度和角速度的数组。
        :return: 包装后的扩展状态观测值。
        """
        # 获取过去状态的数量
        tau_num = len(tau_state)
        # 获取过去动作的数量
        D_num = len(D_actions)
        # 断言检查过去状态的数量是否等于预设的时间窗口长度 self.tau，若不相等则抛出异常
        assert tau_num == self.tau, "dim of tau-state is wrong."
        # 断言检查过去动作的数量是否等于预设的过去动作数量 self.num_of_past_actions，若不相等则抛出异常
        assert D_num == self.num_of_past_actions, "dim of D-action is wrong." 

        # 初始化扩展状态观测值数组，其长度为机器人状态维度 + 2 + 过去动作数量 * 2
        obs = np.zeros(self.car_dim + 2 + self.num_of_past_actions * 2) 
        # 将当前状态（即过去状态列表中的最后一个状态）的 x 坐标减去窗口最大 x 坐标的一半，存入观测值数组的第一个位置
        obs[0] = tau_state[tau_num-1][0] - (self._max_x_of_window/2) 
        # 将当前状态的 y 坐标减去窗口最大 y 坐标的一半，存入观测值数组的第二个位置
        obs[1] = tau_state[tau_num-1][1] - (self._max_y_of_window/2)
        # 将当前状态的偏航角存入观测值数组的第三个位置
        obs[2] = tau_state[tau_num-1][2]

        # 对过去状态数据进行预处理 ===================================
        # 初始化第一个 STL 子公式的标志变量
        f1 = 0.0
        # 初始化第二个 STL 子公式的标志变量
        f2 = 0.0 
        # 遍历过去的每个状态
        for i in range(tau_num):
            # 检查当前状态是否满足第一个 STL 子公式
            if self.subSTL_1_robustness(tau_state[i]) >= 0:
                # 若满足，则将标志变量 f1 置为 1.0
                f1 = 1.0
            else:
                # 若不满足，则将 f1 减去 1/self.tau，并确保其不小于 0.0
                f1 = max(f1 - 1/(float(self.tau)), 0.0)
            # 检查当前状态是否满足第二个 STL 子公式
            if self.subSTL_2_robustness(tau_state[i]) >= 0:
                # 若满足，则将标志变量 f2 置为 1.0
                f2 = 1.0
            else:
                # 若不满足，则将 f2 减去 1/self.tau，并确保其不小于 0.0
                f2 = max(f2 - 1/(float(self.tau)), 0.0)
        # 将第一个 STL 子公式的标志变量减去 0.5 后存入观测值数组的第四个位置
        obs[3] = f1 - 0.5
        # 将第二个 STL 子公式的标志变量减去 0.5 后存入观测值数组的第五个位置
        obs[4] = f2 - 0.5
        # =================================================

        # 遍历过去的每个动作
        for j in range(D_num):
            # 将当前动作的线速度存入观测值数组的相应位置
            obs[5 + 2*j] = D_actions[j][0]
            # 将当前动作的角速度存入观测值数组的相应位置
            obs[6 + 2*j] = D_actions[j][1]

        # 返回包装后的扩展状态观测值
        return obs


    def reward(self, tau_state): 
        """
        根据过去的状态序列评估并计算奖励值，基于信号时序逻辑（STL）公式 F phi₁ ∧ F phi₂ 进行计算。

        :param tau_state: 包含过去多个时刻状态的列表，每个状态是一个数组，代表机器人在某一时刻的状态。
        :return: 基于 STL 公式计算得到的奖励值。
        """
        # 计算过去状态的数量
        tau_num = len(tau_state)
        # 初始化第一个 STL 子公式 F phi₁ 的鲁棒性值，初始值设为 -500，无特殊含义，仅作为极小值
        phi_1_rob = -500. 
        # 初始化第二个 STL 子公式 F phi₂ 的鲁棒性值，初始值设为 -500，无特殊含义，仅作为极小值
        phi_2_rob = -500.

        # 遍历过去的每个状态
        for i in range(tau_num):
            # 计算当前状态下第一个 STL 子公式 phi₁ 的鲁棒性值
            temp_1_rob = self.subSTL_1_robustness(tau_state[i])
            # 计算当前状态下第二个 STL 子公式 phi₂ 的鲁棒性值
            temp_2_rob = self.subSTL_2_robustness(tau_state[i])
            # 取第一个 STL 子公式鲁棒性值的最大值，对应时序逻辑中的最终操作符 F phi₁
            phi_1_rob = max(phi_1_rob, temp_1_rob) 
            # 取第二个 STL 子公式鲁棒性值的最大值，对应时序逻辑中的最终操作符 F phi₂
            phi_2_rob = max(phi_2_rob, temp_2_rob) 

        # 取两个 STL 子公式鲁棒性最大值中的最小值，对应时序逻辑中的合取操作符 F phi₁ ∧ F phi₂
        returns = min(phi_1_rob, phi_2_rob) 

        # 指示函数 I(x)，判断是否满足 STL 公式 F phi₁ ∧ F phi₂
        if returns >= 0:
            # 若满足 STL 公式，奖励值设为 1.0
            returns = 1.0
        else:
            # 若不满足 STL 公式，奖励值设为 0.0
            returns = 0.0

        # 应用指数变换，对应时序逻辑中的全局操作符 G F (...)
        returns = -np.exp(-self.beta*returns) 

        # 返回最终计算得到的奖励值
        return returns


    def evaluate_stl_formula(self): # For evaluation (Success Checking)
        """
        评估当前轨迹是否满足信号时序逻辑（STL）公式 F(phi₁) ∧ F(phi₂)，
        即「最终到达区域1」且「最终到达区域2」，用于策略验证阶段的成功检查。

        :return: 评估结果，1.0 表示满足 STL 公式，0.0 表示不满足。
        """
        # 检查当前环境步数是否达到或超过 tau - 1，确保有足够的历史状态用于评估
        if self.num_steps >= self.tau-1: 
            # 获取过去状态轨迹列表的长度
            tau_num = len(self.past_state_trajectory)
            # 初始化第一个 STL 子公式 F(phi₁) 的最大鲁棒性值，设为极小值
            phi_1_rob = -500.
            # 初始化第二个 STL 子公式 F(phi₂) 的最大鲁棒性值，设为极小值
            phi_2_rob = -500.

            # 遍历过去状态轨迹列表中的每个状态
            for i in range(tau_num):
                # 计算当前状态相对于第一个 STL 子公式 phi₁ 的鲁棒性值
                temp_1_rob = self.subSTL_1_robustness(self.past_state_trajectory[i])
                # 计算当前状态相对于第二个 STL 子公式 phi₂ 的鲁棒性值
                temp_2_rob = self.subSTL_2_robustness(self.past_state_trajectory[i])
                # 更新第一个 STL 子公式 F(phi₁) 的最大鲁棒性值
                phi_1_rob = max(phi_1_rob, temp_1_rob)
                # 更新第二个 STL 子公式 F(phi₂) 的最大鲁棒性值
                phi_2_rob = max(phi_2_rob, temp_2_rob)

            # 取两个 STL 子公式最大鲁棒性值中的最小值，对应合取操作 F(phi₁) ∧ F(phi₂)
            returns = min(phi_1_rob, phi_2_rob)

            # 根据最小鲁棒性值判断是否满足 STL 公式
            if returns >= 0:
                # 若最小鲁棒性值非负，说明满足 STL 公式，返回 1.0
                returns = 1.0
            else:
                # 若最小鲁棒性值为负，说明不满足 STL 公式，返回 0.0
                returns = 0.0

        else: # t < tau-1 
            # 若环境步数小于 tau - 1，历史状态不足，暂不检查部分轨迹，默认满足 STL 公式
            # If the num_step is less than tau-1, we do not check the past partial trajectories.
            returns = 1.0

        return returns


    def subSTL_1_robustness(self, state):
        """
        计算状态相对于第一个信号时序逻辑（STL）子公式的鲁棒性值。
        此子公式定义了一个二维平面上的矩形区域，鲁棒性值反映了状态点与该区域的位置关系。
        正值表示状态点在区域内，负值表示在区域外，值越大鲁棒性越强。

        :param state: 包含状态信息的数组，state[0] 为 x 坐标，state[1] 为 y 坐标。
        :return: 状态相对于第一个 STL 子公式的鲁棒性值。
        """
        # 计算状态的 x 坐标减去第一个 STL 区域的 x 坐标下限，大于 0 表示在区域右方
        psi1 = state[0] - self.stl_1_low_x 
        # 计算第一个 STL 区域的 x 坐标上限减去状态的 x 坐标，大于 0 表示在区域左方
        psi2 = self.stl_1_high_x - state[0] 
        # 计算状态的 y 坐标减去第一个 STL 区域的 y 坐标下限，大于 0 表示在区域上方
        psi3 = state[1] - self.stl_1_low_y 
        # 计算第一个 STL 区域的 y 坐标上限减去状态的 y 坐标，大于 0 表示在区域下方
        psi4 = self.stl_1_high_y - state[1] 
        # 先取 x 坐标相关的两个差值中的最小值，确定在 x 方向上距离区域边界的最近距离
        robustness = min(psi1, psi2)
        # 将上述最小值与 y 坐标相关的第一个差值取最小值，进一步确定距离区域边界的最近距离
        robustness = min(robustness, psi3)
        # 最后将上述最小值与 y 坐标相关的第二个差值取最小值，得到最终的鲁棒性值
        robustness = min(robustness, psi4)
        return robustness
    
    def subSTL_2_robustness(self, state):
        """
        计算状态相对于第二个信号时序逻辑（STL）子公式的鲁棒性值。
        此子公式同样定义了一个二维平面上的矩形区域，鲁棒性值反映了状态点与该区域的位置关系。
        正值表示状态点在区域内，负值表示在区域外，值越大鲁棒性越强。

        :param state: 包含状态信息的数组，state[0] 为 x 坐标，state[1] 为 y 坐标。
        :return: 状态相对于第二个 STL 子公式的鲁棒性值。
        """
        # 计算状态的 x 坐标减去第二个 STL 区域的 x 坐标下限，大于 0 表示在区域右方
        psi1 = state[0] - self.stl_2_low_x 
        # 计算第二个 STL 区域的 x 坐标上限减去状态的 x 坐标，大于 0 表示在区域左方
        psi2 = self.stl_2_high_x - state[0] 
        # 计算状态的 y 坐标减去第二个 STL 区域的 y 坐标下限，大于 0 表示在区域上方
        psi3 = state[1] - self.stl_2_low_y 
        # 计算第二个 STL 区域的 y 坐标上限减去状态的 y 坐标，大于 0 表示在区域下方
        psi4 = self.stl_2_high_y - state[1] 
        # 先取 x 坐标相关的两个差值中的最小值，确定在 x 方向上距离区域边界的最近距离
        robustness = min(psi1, psi2)
        # 将上述最小值与 y 坐标相关的第一个差值取最小值，进一步确定距离区域边界的最近距离
        robustness = min(robustness, psi3)
        # 最后将上述最小值与 y 坐标相关的第二个差值取最小值，得到最终的鲁棒性值
        robustness = min(robustness, psi4)
        return robustness


    """ def render(self, mode='human', close=False): 
        screen_width  = 300
        screen_height = 300

        rate_x = screen_width / self._max_x_of_window
        rate_y = screen_height / self._max_y_of_window 

        rate_init_l = self.init_low_x / self._max_x_of_window
        rate_init_r = self.init_high_x / self._max_x_of_window
        rate_init_t = self.init_high_y / self._max_y_of_window
        rate_init_b = self.init_low_y / self._max_y_of_window
        rate_stl_1_l = self.stl_1_low_x / self._max_x_of_window
        rate_stl_1_r = self.stl_1_high_x / self._max_x_of_window
        rate_stl_1_t = self.stl_1_high_y / self._max_y_of_window
        rate_stl_1_b = self.stl_1_low_y / self._max_y_of_window
        rate_stl_2_l = self.stl_2_low_x / self._max_x_of_window
        rate_stl_2_r = self.stl_2_high_x / self._max_x_of_window
        rate_stl_2_t = self.stl_2_high_y / self._max_y_of_window
        rate_stl_2_b = self.stl_2_low_y / self._max_y_of_window

        if self.viewer is None:
            self.viewer = rendering.Viewer(screen_width, screen_height)

            # start
            start_l, start_r, start_t, start_b = rate_init_l*screen_width, rate_init_r*screen_width, rate_init_t*screen_height, rate_init_b*screen_height  # screen のサイズで与える．
            self.start_area = [(start_l,start_b), (start_l,start_t), (start_r,start_t), (start_r,start_b)]
            start = rendering.make_polygon(self.start_area)
            self.start_area_trans = rendering.Transform()
            start.add_attr(self.start_area_trans)
            start.set_color(0.8, 0.5, 0.5)
            self.viewer.add_geom(start)

            # goal_1
            g1_l, g1_r, g1_t, g1_b = rate_stl_1_l*screen_width, rate_stl_1_r*screen_width, rate_stl_1_t*screen_height, rate_stl_1_b*screen_height  # screen のサイズで与える．
            self.v1 = [(g1_l,g1_b), (g1_l,g1_t), (g1_r,g1_t), (g1_r,g1_b)]
            g1 = rendering.make_polygon(self.v1)
            self.g1trans = rendering.Transform()
            g1.add_attr(self.g1trans)
            g1.set_color(0.5, 0.5, 0.8)
            self.viewer.add_geom(g1)

            # goal_2
            g2_l, g2_r, g2_t, g2_b = rate_stl_2_l*screen_width, rate_stl_2_r*screen_width, rate_stl_2_t*screen_height, rate_stl_2_b*screen_height  # screen のサイズで与える．
            self.v2 = [(g2_l,g2_b), (g2_l,g2_t), (g2_r,g2_t), (g2_r,g2_b)]
            g2 = rendering.make_polygon(self.v2)
            self.g2trans = rendering.Transform()
            g2.add_attr(self.g2trans)
            g2.set_color(0.5, 0.5, 0.8)
            self.viewer.add_geom(g2)

            head_x = (self.robot_radius) * rate_x
            head_y = 0.0 * rate_y
            tail_left_x = (self.robot_radius*np.cos((5/6)+np.pi)) * rate_x
            tail_left_y = (self.robot_radius*np.sin((5/6)+np.pi)) * rate_y
            tail_right_x = (self.robot_radius*np.cos(-(5/6)+np.pi)) * rate_x
            tail_right_y = (self.robot_radius*np.sin(-(5/6)+np.pi)) * rate_y
            self.car_v = [(head_x,head_y), (tail_left_x,tail_left_y), (tail_right_x,tail_right_y)]
            car = rendering.FilledPolygon(self.car_v)
            self.cartrans = rendering.Transform()
            car.add_attr(self.cartrans)
            car.set_color(1.0, 0.0, 0.0)
            self.viewer.add_geom(car)

        car_x = self.state[0] * rate_x
        car_y = self.state[1] * rate_y
   

        self.cartrans.set_translation(car_x, car_y)
        self.cartrans.set_rotation(self.state[2])

        return self.viewer.render(return_rgb_array = mode=='rgb_array') """

    def close(self):
        if self.viewer:
            self.viewer.close()
            self.viewer = None

    def seed(self, seed=None):
        self.np_random, seed = seeding.np_random(seed)
        return [seed]
