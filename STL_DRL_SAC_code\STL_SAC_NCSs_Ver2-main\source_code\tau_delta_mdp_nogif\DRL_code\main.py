import sys
import os
# 将当前文件所在目录的上一级目录添加到 Python 模块搜索路径中
# 这样可以确保项目内的自定义模块能够被正确导入
sys.path.append(os.path.join(os.path.dirname(__file__) + '/../'))

import numpy as np  # 导入 numpy 库，用于数值计算
import torch  # 导入 PyTorch 库，用于深度学习相关操作
import random  # 导入 random 库，用于生成随机数

import gym  # 导入 OpenAI Gym 库，用于创建和管理强化学习环境
import gym_pathplan  # 导入自定义的 Gym 环境模块
# 设置 Gym 日志级别为 40（ERROR），只显示错误信息，减少不必要的日志输出
gym.logger.set_level(40) 

import fixed_seed  # 导入自定义的固定随机种子模块
import trainer  # 导入自定义的训练器模块
import sac  # 导入自定义的软演员 - 评论家（SAC）算法模块

def main():
    # 定义要使用的 Gym 环境 ID，'STLPathPlan-v0' 表示未预处理的环境
    ENV_ID = 'STLPathPlan-v0' # nopreprocess
    # 注释掉的环境 ID，'STLPathPlan-v1' 表示经过预处理的环境
    #ENV_ID = 'STLPathPlan-v1' # preprocess
    # 设置随机数种子，取值范围为 0 到 14，这里设置为 14
    SEED = 14 # 0,1,...,14

    # 定义训练的总步数为 600000
    NUM_STEPS = 6 * 10 ** 5
    # 定义评估模型的间隔步数为 10000
    EVAL_INTERVAL = 10 ** 4
    # 定义训练时每个批次的样本数量为 64
    BATCH_SIZE = 64
    # 定义折扣因子，用于计算累积奖励，值为 0.99
    GAMMA = 0.99
    # 定义演员网络的学习率为 0.0003
    LEARNING_RATE_ACTOR = 3e-4
    # 定义评论家网络的学习率为 0.0003
    LEARNING_RATE_CRITIC = 3e-4
    # 定义回放缓冲区的最大容量为 100000
    REPLAY_BUFFER_SIZE = 10**5
    # 定义目标网络软更新的系数为 0.01
    TAU = 0.01
    # 定义奖励缩放因子为 1.0
    REWARD_SCALE = 1.0
    # 定义每次评估时运行的回合数为 100
    NUM_EVAL_EPISODES = 100

    # 创建用于训练的 Gym 环境实例
    env = gym.make(ENV_ID)
    # 创建用于测试的 Gym 环境实例
    env_test = gym.make(ENV_ID)

    # 为 numpy、random 和 PyTorch 设置随机数种子，确保实验可复现
    fixed_seed.fixed_seed_function(SEED)
    # 为训练环境设置随机数种子
    env.seed(SEED)
    # 为训练环境的动作空间设置随机数种子
    env.action_space.seed(SEED)
    # 为训练环境的观测空间设置随机数种子
    env.observation_space.seed(SEED)
    # 为测试环境设置随机数种子，使用 2**31 - SEED 以保证与训练环境种子不同
    env_test.seed(2**31-SEED)
    # 为测试环境的动作空间设置随机数种子
    env_test.action_space.seed(2**31-SEED)
    # 为测试环境的观测空间设置随机数种子
    env_test.observation_space.seed(2**31-SEED)

    # 打印训练环境观测空间的形状
    print(env.observation_space.shape)
    # 打印训练环境扩展状态空间的形状
    print(env.extended_state_space.shape)
    # 初始化 SAC 算法实例
    algo = sac.SAC(
        # 传入扩展状态空间的形状作为状态维度
        state_shape=env.extended_state_space.shape,
        # 传入动作空间的形状作为动作维度
        action_shape=env.action_space.shape,
        # 设置随机数种子
        seed=SEED,
        # 设置训练批次大小
        batch_size=BATCH_SIZE,
        # 设置折扣因子
        gamma=GAMMA,
        # 设置演员网络的学习率
        lr_actor=LEARNING_RATE_ACTOR,
        # 设置评论家网络的学习率
        lr_critic=LEARNING_RATE_CRITIC,
        # 设置回放缓冲区的大小
        replay_size=REPLAY_BUFFER_SIZE,
        # 设置目标网络软更新的系数
        tau=TAU,
        # 设置奖励缩放因子
        reward_scale=REWARD_SCALE,
        # 启用自动调整熵系数
        auto_coef=True,
    )


    # 定义训练器实例，用于管理整个训练流程
    # Define Trainer
    SAC_trainer = trainer.Trainer(
        env=env,  # 传入训练使用的 Gym 环境实例
        env_test=env_test,  # 传入测试使用的 Gym 环境实例
        algo=algo,  # 传入初始化好的 SAC 算法实例
        seed=SEED,  # 设置随机数种子，保证实验可复现性
        num_steps=NUM_STEPS,  # 设置训练的总步数
        eval_interval=EVAL_INTERVAL,  # 设置评估模型的间隔步数
        num_eval_episodes=NUM_EVAL_EPISODES,  # 设置每次评估运行的回合数
    )

    # 调用训练器的 train 方法，开始执行训练过程
    SAC_trainer.train() # Learning
    # 调用训练器的 plot 方法，对训练结果进行可视化展示
    SAC_trainer.plot() # Result

    # 关闭训练环境，释放相关资源
    env.close()
    # 关闭测试环境，释放相关资源
    env_test.close()

# 确保以下代码仅在脚本作为主程序直接运行时执行
if __name__ == "__main__":
    # 调用 main 函数，启动整个训练流程
    main()
