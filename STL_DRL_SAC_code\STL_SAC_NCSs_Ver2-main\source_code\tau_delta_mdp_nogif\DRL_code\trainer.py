# 从 abc 模块导入抽象基类 ABC 和抽象方法装饰器 abstractmethod
from abc import ABC, abstractmethod
# 导入 matplotlib 库的 pyplot 模块，用于绘图
import matplotlib.pyplot as plt
# 从 matplotlib 库导入 animation 模块，用于创建动画
from matplotlib import animation
# 从 time 模块导入 time 函数，用于获取当前时间戳
from time import time
# 从 datetime 模块导入 timedelta 类，用于处理时间间隔
from datetime import timedelta
# 导入 numpy 库，用于数值计算
import numpy as np
# 导入 torch 库，用于深度学习相关操作
import torch
# 导入 pandas 库，用于数据处理和分析
import pandas as pd

class Trainer:
    """
    训练器类，用于管理强化学习算法的训练过程，
    包括环境交互、策略评估和模型更新等操作。
    """

    def __init__(self, env, env_test, algo, seed=0, num_steps=10**6, eval_interval=10**4, num_eval_episodes=1):
        """
        初始化训练器。

        :param env: 训练环境实例。
        :param env_test: 测试环境实例。
        :param algo: 强化学习算法实例。
        :param seed: 随机数种子，默认为 0，当前未使用。
        :param num_steps: 总的训练步数，默认为 1000000。
        :param eval_interval: 策略评估的间隔步数，默认为 10000。
        :param num_eval_episodes: 每次评估时的测试回合数，默认为 1。
        """
        # 存储训练环境实例
        self.env = env
        # 存储测试环境实例
        self.env_test = env_test
        # 存储强化学习算法实例
        self.algo = algo

        # 初始化一个字典，用于记录训练过程中的步数、平均回报和成功率
        self.returns = {'step': [], 'return': [], 'success_rate':[]}

        # 存储总的训练步数
        self.num_steps = num_steps
        # 存储策略评估的间隔步数
        self.eval_interval = eval_interval
        # 存储每次评估时的测试回合数
        self.num_eval_episodes = num_eval_episodes

    def train(self):
        """
        训练强化学习算法的主循环，负责与环境交互、更新策略和定期评估策略。
        """
        # 记录训练开始的时间戳
        self.start_time = time()

        # 初始化时间步计数器
        t = 0

        # 重置训练环境，获取初始状态
        state = self.env.reset()

        # 开始训练循环，从第 1 步到指定的总训练步数
        for steps in range(1, self.num_steps + 1): # num_steps = 6 * 10 ** (5)
            # 调用算法的 step 方法，与环境进行交互，更新状态和时间步计数器
            state, t = self.algo.step(self.env, state, t, steps)

            # 检查是否满足算法更新条件
            if self.algo.is_update(steps):
                # 若满足条件，调用算法的 update 方法进行参数更新
                self.algo.update()

            # 每间隔 eval_interval 步对学习到的策略进行评估
            if steps % self.eval_interval == 0:

                self.evaluate(steps)
        #self.save_gif() # save gif for final policy
    
    # def save_gif(self):
    #     images = []
    #     state = self.env_test.reset()
    #     done = False

    #     while(not done):
    #         images.append(self.env_test.render(mode='rgb_array'))
    #         action = self.algo.exploit(state)
    #         state, reward, done, _ = self.env_test.step(action)
    #     self.display_video(images)

    # def display_video(self, frames):
    #     plt.figure(figsize=(8, 8), dpi=50)
    #     patch = plt.imshow(frames[0])
    #     plt.axis('off')

    #     def animate(i):
    #         patch.set_data(frames[i])

    #     anim = animation.FuncAnimation(plt.gcf(), animate, frames=len(frames), interval=50)

    #     anim.save('env.gif', writer='PillowWriter')  

    def evaluate(self, steps):
        """
        在测试环境中评估当前学习到的策略。

        :param steps: 当前的训练步数，用于记录评估结果。
        """
        # 存储每个测试回合的累积回报
        returns = []
        # 存储每个测试回合的 STL 公式评估结果
        evaluates = []
        # 折扣因子，用于计算累积回报
        GAMMA = 0.99

        # 进行多次测试回合，回合数由 num_eval_episodes 决定
        for _ in range(self.num_eval_episodes):  # 100 episodes for policy evaluation
            # 初始化 STL 公式评估结果，初始值为 1.0
            evaluate_val = 1.0 
            # 重置测试环境，获取初始状态
            state = self.env_test.reset()
            # 调用环境的 evaluate_stl_formula 方法评估 STL 公式，初始返回 1.0
            eval_temp = self.env_test.evaluate_stl_formula() 
            # 取当前评估结果和之前结果的最小值，对应时序逻辑中的全局操作符 G\phi
            evaluate_val = min(evaluate_val, eval_temp) 
            # 标记回合是否结束
            done = False
            # 初始化当前回合的累积回报
            episode_return = 0.0
            # 折扣因子的指数计数器
            gamma_count = 0

            # 当回合未结束时，持续执行动作
            while (not done):
                # 使用算法的 exploit 方法选择最优动作
                action = self.algo.exploit(state)
                # 在测试环境中执行动作，获取下一个状态、奖励、回合结束标志等信息
                state, reward, done, _ = self.env_test.step(action)
                # 再次评估 STL 公式，如果过去状态序列不满足 STL 规范，返回 0.0
                eval_temp = self.env_test.evaluate_stl_formula() 
                # 取当前评估结果和之前结果的最小值，对应时序逻辑中的全局操作符 G\phi
                evaluate_val = min(evaluate_val, eval_temp) 
                # 累加折扣后的奖励到当前回合的累积回报中
                episode_return += (GAMMA**(gamma_count)) * reward
                # 折扣因子的指数计数器加 1
                gamma_count += 1

            # 将当前回合的 STL 公式评估结果添加到 evaluates 列表中
            evaluates.append(evaluate_val)
            # 将当前回合的累积回报添加到 returns 列表中
            returns.append(episode_return)

        # 计算所有测试回合累积回报的平均值
        mean_return = np.mean(returns)
        # 计算所有测试回合 STL 公式评估结果的平均值，即成功率
        success_rate = np.mean(evaluates)
        # 将当前训练步数添加到 returns 字典的 'step' 列表中
        self.returns['step'].append(steps)
        # 将平均回报添加到 returns 字典的 'return' 列表中
        self.returns['return'].append(mean_return)
        # 将成功率添加到 returns 字典的 'success_rate' 列表中
        self.returns['success_rate'].append(success_rate)

        # 打印当前训练步数、平均回报、成功率和训练耗时
        print(f'Num steps: {steps:<6}   '
              f'Return: {mean_return:<5.1f}   '
              f'Success Rate: {success_rate:<5.2f}   '
              f'Time: {self.time}')
        # 每训练 100000 步，备份一次算法模型
        if steps % 100000 == 0:    
            self.algo.backup_model(steps)

    def plot(self):
        """
        将训练过程中的平均回报和成功率保存到 CSV 文件中。
        """
        # 将 returns 字典中的 'return' 列表转换为 DataFrame
        datasets = pd.DataFrame(self.returns['return'])
        # 将平均回报数据保存到 returns.csv 文件中
        datasets.to_csv('returns.csv', mode='w')
        # 将 returns 字典中的 'success_rate' 列表转换为 DataFrame
        datasets = pd.DataFrame(self.returns['success_rate'])
        # 将成功率数据保存到 success.csv 文件中
        datasets.to_csv('success.csv', mode='w')

    @property
    def time(self):
        """
        计算从训练开始到现在所经过的时间。

        :return: 以字符串形式表示的训练耗时，格式为 HH:MM:SS。
        """
        return str(timedelta(seconds=int(time() - self.start_time)))


class Algorithm(ABC):
    """
    强化学习算法的抽象基类，定义了强化学习算法应实现的核心方法。
    继承该类的具体算法需要实现所有的抽象方法，以确保具备探索、利用、更新等基本功能。
    """

    @abstractmethod
    def explore(self, state):
        """
        探索环境的方法，在训练过程中以一定的策略进行探索性动作选择。
        通常会引入随机性，帮助智能体发现新的状态和动作，以探索更优的策略。

        :param state: 当前环境的状态。
        :return: 智能体选择的探索性动作。
        """
        pass

    @abstractmethod
    def exploit(self, state):
        """
        利用已有知识的方法，在测试或评估阶段选择当前认为最优的动作。
        基于已学习到的策略，以最大化累积奖励。

        :param state: 当前环境的状态。
        :return: 智能体选择的最优动作。
        """
        pass

    @abstractmethod
    def is_update(self, steps):
        """
        判断是否满足算法参数更新条件的方法。
        根据当前的训练步数，决定是否需要对算法的模型参数进行更新。

        :param steps: 当前的训练步数。
        :return: 布尔值，若满足更新条件返回 True，否则返回 False。
        """
        pass

    @abstractmethod
    def step(self, env, state, t, steps):
        """
        执行一个时间步的交互操作，与环境进行一次交互并更新状态。
        包括选择动作、执行动作、获取奖励和下一个状态等操作。

        :param env: 环境实例，用于与智能体进行交互。
        :param state: 当前环境的状态。
        :param t: 当前回合的时间步计数器。
        :param steps: 当前的训练总步数。
        :return: 元组，包含下一个状态和更新后的回合时间步计数器。
        """
        pass

    @abstractmethod
    def update(self):
        """
        更新算法模型参数的方法。
        根据智能体与环境交互的经验数据，使用特定的优化算法更新模型的参数。
        """
        pass

    @abstractmethod
    def backup_model(self, steps):
        """
        备份算法模型的方法。
        根据当前的训练步数，将算法的模型参数保存到文件中，以便后续恢复或分析。

        :param steps: 当前的训练步数。
        """
        pass
