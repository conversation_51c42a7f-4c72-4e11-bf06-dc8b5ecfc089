# Limo机器人STL约束避障导航融合方案

## 概述

本项目将STL（Signal Temporal Logic）时序逻辑约束与Limo机器人的避障导航任务相融合，实现了一个基于拉格朗日TD3算法的两阶段训练方案。机器人在Gazebo仿真环境中既能完成避障导航，又能满足复杂的STL时序逻辑约束。

## 主要特性

### 1. 扩展状态空间
- **原始状态**: [激光雷达(20) + 导航信息(3) + 过去动作(2)] = 25维
- **扩展状态**: [激光雷达(20) + 导航信息(3) + 位置信息(3) + STL标志(2) + 过去动作(2)] = 30维
- 新增位置信息用于STL约束计算
- 新增STL标志位反映约束满足情况

### 2. 双重奖励系统
- **导航奖励**: 距离奖励 + 方向奖励 + 避障奖励
- **STL奖励**: 基于时序逻辑约束的鲁棒性计算

### 3. 双Critic架构
- **导航Critic**: 学习导航任务的价值函数
- **STL Critic**: 学习STL约束的价值函数
- 分别优化不同目标，避免冲突

### 4. 两阶段训练策略
- **预训练阶段** (0-250k步): 专注于STL约束学习
- **微调阶段** (250k+步): 使用拉格朗日方法平衡导航和STL目标

## 文件结构

```
TD3/
├── Environment.py              # 修改后的环境类，支持STL计算
├── lagrangian_td3.py          # 拉格朗日TD3算法实现
├── buffer.py                  # 修改后的经验回放缓冲区
├── train_lagrangian_td3.py    # 训练脚本
├── test_lagrangian_td3.py     # 测试脚本
├── TD3Net.py                  # 原始TD3网络（保留）
└── README_STL_Fusion.md       # 本文档
```

## 核心修改

### 1. Environment.py 主要修改
- 添加STL相关参数和区域定义
- 扩展`getState()`方法，增加位置信息和STL标志
- 修改`setReward()`方法，返回双重奖励
- 新增STL鲁棒性计算方法
- 修改`step()`方法适应新的奖励结构

### 2. buffer.py 主要修改
- 支持存储双重奖励（导航奖励和STL奖励）
- 修改采样方法返回两种奖励

### 3. lagrangian_td3.py 核心算法
- 实现双Critic架构
- 两阶段训练逻辑
- 拉格朗日乘数自适应更新
- 支持预训练和微调模式切换

## 使用方法

### 1. 训练模型

```bash
# 进入脚本目录
cd limo_ws/src/limoRL/scripts/TD3

# 启动Gazebo仿真环境（另一个终端）
roslaunch limo_gazebo_sim limo_four_diff.launch

# 开始训练
python3 train_lagrangian_td3.py
```

### 2. 测试模型

```bash
# 启动Gazebo仿真环境
roslaunch limo_gazebo_sim limo_four_diff.launch

# 运行测试
python3 test_lagrangian_td3.py
```

### 3. 参数配置

主要超参数在训练脚本中可以调整：

```python
# 训练参数
pretrain_steps = 250000      # 预训练步数
threshold = -40.0            # STL约束阈值
lr_kappa = 1e-5             # 拉格朗日乘数学习率

# STL区域定义（在Environment.py中）
self.stl_1_low_x = 3.0      # 区域1边界
self.stl_1_high_x = 4.0
self.stl_1_low_y = 3.0
self.stl_1_high_y = 4.0

self.stl_2_low_x = 3.0      # 区域2边界
self.stl_2_high_x = 4.0
self.stl_2_low_y = -2.0
self.stl_2_high_y = -1.0
```

## STL约束定义

当前实现的STL公式为：
- **φ₁**: 机器人最终访问区域1 [3,4] × [3,4]
- **φ₂**: 机器人最终访问区域2 [3,4] × [-2,-1]
- **总约束**: φ = φ₁ ∧ φ₂ (两个区域都要访问)

## 训练过程监控

训练过程中会打印以下信息：
- Episode数和步数
- 导航奖励和STL奖励
- 当前训练阶段（预训练/微调）
- 拉格朗日乘数κ的值
- 总训练步数

## 性能指标

测试脚本会计算以下指标：
- **成功率**: 到达目标的比例
- **碰撞率**: 发生碰撞的比例
- **超时率**: 超过最大步数的比例
- **平均回合长度**: 每回合的平均步数
- **平均奖励**: 导航奖励和STL奖励的平均值

## 注意事项

1. **坐标系统**: 确保STL区域定义与Gazebo环境坐标系一致
2. **训练时间**: 完整训练需要较长时间，建议使用GPU加速
3. **内存使用**: 经验回放缓冲区较大，注意内存使用情况
4. **模型保存**: 每1000回合自动保存模型，可根据需要调整
5. **ROS依赖**: 确保ROS环境正确配置，Gazebo仿真正常运行

## 故障排除

1. **导入错误**: 确保所有依赖包已安装（torch, numpy, rospy等）
2. **ROS连接**: 检查ROS master是否运行，话题是否正确
3. **Gazebo问题**: 确保Gazebo仿真环境正常启动
4. **内存不足**: 减小batch_size或replay buffer大小
5. **训练不收敛**: 调整学习率或网络结构参数

## 扩展建议

1. **更复杂STL约束**: 可以定义更复杂的时序逻辑约束
2. **动态障碍物**: 在环境中添加动态障碍物
3. **多机器人**: 扩展到多机器人协作场景
4. **实际部署**: 将训练好的模型部署到真实Limo机器人
5. **可视化**: 添加训练过程和STL约束满足情况的可视化
