import torch
from torch import nn
from torch.distributions import Normal
import torch.nn.functional as F
import utility

class SACActor(nn.Module):
    """
    软演员-评论家（Soft Actor-Critic, SAC）算法中的演员网络，用于生成动作。
    该网络接收环境状态作为输入，输出动作的均值和对数标准差，
    并通过重参数化技巧采样得到动作。
    """

    def __init__(self, state_shape, action_shape):
        """
        初始化 SAC 演员网络。

        :param state_shape: 状态的形状，通常为一个元组，用于确定输入层的神经元数量。
        :param action_shape: 动作的形状，通常为一个元组，用于确定输出层的神经元数量。
        """
        super().__init__()

        # 定义神经网络结构
        self.net = nn.Sequential(
            # 输入层到第一个隐藏层，输入维度为状态的维度，输出维度为 256
            nn.Linear(state_shape[0], 256),
            # 使用 ReLU 激活函数，inplace=True 表示直接修改输入，节省内存
            nn.ReLU(inplace=True),
            # 第一个隐藏层到第二个隐藏层，输入维度为 256，输出维度为 256
            nn.Linear(256, 256),
            # 使用 ReLU 激活函数
            nn.ReLU(inplace=True),
            # 第二个隐藏层到输出层，输入维度为 256，输出维度为动作维度的两倍
            # 输出动作的均值和对数标准差
            nn.Linear(256, 2 * action_shape[0]),
        )

    def forward(self, states):
        """
        前向传播方法，用于确定性地生成动作。

        :param states: 输入的环境状态张量。
        :return: 经过双曲正切函数处理后的动作均值张量。
        """
        # 通过网络计算输出，并将输出沿最后一个维度分成两部分，取第一部分作为均值
        # 再使用双曲正切函数将动作限制在 [-1, 1] 范围内
        return torch.tanh(self.net(states).chunk(2, dim=-1)[0])

    def sample(self, states):
        """
        采样方法，使用重参数化技巧从动作分布中采样得到动作。

        :param states: 输入的环境状态张量。
        :return: 通过重参数化技巧采样得到的动作。
        """
        # 通过网络计算输出，并将输出沿最后一个维度分成两部分，分别得到均值和对数标准差
        means, log_stds = self.net(states).chunk(2, dim=-1)
        # 对对数标准差进行裁剪，限制在 [-20, 2] 范围内，避免数值不稳定
        # 调用 utility 模块中的重参数化函数生成动作
        return utility.reparameterize(means, log_stds.clamp(-20, 2))


class SACCritic(nn.Module):
    """
    软演员-评论家（Soft Actor-Critic, SAC）算法中的评论家网络，用于评估动作价值。
    该网络包含两个独立的子网络，用于减少高估偏差。
    """

    def __init__(self, state_shape, action_shape):
        """
        初始化 SAC 评论家网络。

        :param state_shape: 状态的形状，通常为一个元组，用于确定输入层的神经元数量。
        :param action_shape: 动作的形状，通常为一个元组，用于确定输入层的神经元数量。
        """
        super().__init__()

        # 定义第一个评论家子网络
        self.net1 = nn.Sequential(
            # 输入层到第一个隐藏层，输入维度为状态维度与动作维度之和，输出维度为 256
            nn.Linear(state_shape[0] + action_shape[0], 256),
            # 使用 ReLU 激活函数
            nn.ReLU(inplace=True),
            # 第一个隐藏层到第二个隐藏层，输入维度为 256，输出维度为 256
            nn.Linear(256, 256),
            # 使用 ReLU 激活函数
            nn.ReLU(inplace=True),
            # 第二个隐藏层到输出层，输入维度为 256，输出维度为 1，输出动作价值
            nn.Linear(256, 1),
        )
        # 定义第二个评论家子网络，结构与第一个相同
        self.net2 = nn.Sequential(
            nn.Linear(state_shape[0] + action_shape[0], 256),
            nn.ReLU(inplace=True),
            nn.Linear(256, 256),
            nn.ReLU(inplace=True),
            nn.Linear(256, 1),
        )

    def forward(self, states, actions):
        """
        前向传播方法，计算动作价值。

        :param states: 输入的环境状态张量。
        :param actions: 输入的动作张量。
        :return: 两个子网络分别计算得到的动作价值张量。
        """
        # 将状态和动作张量在最后一个维度拼接
        x = torch.cat([states, actions], dim=-1)
        # 分别通过两个子网络计算动作价值
        return self.net1(x), self.net2(x)
