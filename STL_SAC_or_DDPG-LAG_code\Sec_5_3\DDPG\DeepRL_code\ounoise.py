import numpy as np


# from https://github.com/songrotek/DDPG/blob/master/ou_noise.py
class OU_NOISE:

    def __init__(self, action_dimension, scale=1.0, mu=0, theta=0.15, sigma=0.3): #default scale 0.1
        self.action_dimension = action_dimension
        self.scale = scale
        self.mu = mu
        self.theta = theta
        self.sigma = sigma
        self.state = np.ones(self.action_dimension) * self.mu
        self.reset()

    def reset(self):
        self.state = np.ones(self.action_dimension) * self.mu

    def noise(self):
        x = self.state
        dx = self.theta * (self.mu - x) + self.sigma * np.random.randn(len(x))
        self.state = x + dx
        return self.state * self.scale