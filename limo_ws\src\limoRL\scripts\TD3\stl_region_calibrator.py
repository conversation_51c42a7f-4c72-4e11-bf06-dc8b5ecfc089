#!/usr/bin/env python3

import rospy
import numpy as np
import json
import cv2
from sensor_msgs.msg import LaserScan
from geometry_msgs.msg import PoseStamped, PointStamped
from nav_msgs.msg import OccupancyGrid, Odometry
from std_msgs.msg import String
from visualization_msgs.msg import Marker, MarkerArray
import tf2_ros
import tf2_geometry_msgs
from typing import List, Tuple, Dict
from stl_config import STLRegion, STLConfig

class STLRegionCalibrator:
    """
    STL区域标定工具
    用于在真实环境中标定STL任务区域
    """
    
    def __init__(self):
        rospy.init_node('stl_region_calibrator', anonymous=True)
        
        # 机器人位置
        self.current_position = None
        self.current_orientation = None
        
        # 标定数据
        self.calibration_points = []
        self.defined_regions = []
        self.current_region_points = []
        self.calibration_mode = "IDLE"  # IDLE, DEFINING_REGION, COMPLETED
        
        # 地图数据
        self.map_data = None
        self.map_info = None
        
        # ROS通信设置
        self.setup_ros_communication()
        
        # TF监听器
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)
        
        print("STL区域标定工具初始化完成")
        print("使用说明:")
        print("1. 发布 'start_region' 到 /calibration_command 开始定义区域")
        print("2. 使用 rviz 的 2D Nav Goal 工具点击区域边界点")
        print("3. 发布 'finish_region' 完成当前区域定义")
        print("4. 发布 'save_config' 保存配置")
    
    def setup_ros_communication(self):
        """设置ROS通信"""
        # 订阅者
        self.pose_sub = rospy.Subscriber('/amcl_pose', PoseStamped, self.pose_callback)
        self.goal_sub = rospy.Subscriber('/move_base_simple/goal', PoseStamped, self.goal_callback)
        self.map_sub = rospy.Subscriber('/map', OccupancyGrid, self.map_callback)
        self.command_sub = rospy.Subscriber('/calibration_command', String, self.command_callback)
        
        # 发布者
        self.marker_pub = rospy.Publisher('/stl_regions_markers', MarkerArray, queue_size=1)
        self.status_pub = rospy.Publisher('/calibration_status', String, queue_size=1)
        self.config_pub = rospy.Publisher('/stl_config_output', String, queue_size=1)
        
        # 定时器
        self.marker_timer = rospy.Timer(rospy.Duration(0.5), self.publish_markers)
    
    def pose_callback(self, data: PoseStamped):
        """位姿回调"""
        self.current_position = data.pose.position
        self.current_orientation = data.pose.orientation
    
    def goal_callback(self, data: PoseStamped):
        """目标点回调（用于标定区域边界点）"""
        if self.calibration_mode == "DEFINING_REGION":
            point = (data.pose.position.x, data.pose.position.y)
            self.current_region_points.append(point)
            print(f"添加区域点: ({point[0]:.2f}, {point[1]:.2f})")
            self.publish_status(f"区域点数: {len(self.current_region_points)}")
    
    def map_callback(self, data: OccupancyGrid):
        """地图回调"""
        self.map_data = data.data
        self.map_info = data.info
    
    def command_callback(self, data: String):
        """命令回调"""
        command = data.data.strip().lower()
        
        if command == "start_region":
            self.start_region_definition()
        elif command == "finish_region":
            self.finish_region_definition()
        elif command == "save_config":
            self.save_configuration()
        elif command == "clear_all":
            self.clear_all_regions()
        elif command == "show_current":
            self.show_current_position()
        elif command.startswith("set_region_name:"):
            region_name = command.split(":", 1)[1].strip()
            self.set_current_region_name(region_name)
        elif command.startswith("set_region_type:"):
            region_type = command.split(":", 1)[1].strip()
            self.set_current_region_type(region_type)
        else:
            print(f"未知命令: {command}")
    
    def start_region_definition(self):
        """开始定义区域"""
        if self.calibration_mode == "DEFINING_REGION":
            print("已经在定义区域中，请先完成当前区域")
            return
        
        self.calibration_mode = "DEFINING_REGION"
        self.current_region_points = []
        print("开始定义新区域，请使用rviz的2D Nav Goal工具点击区域边界点")
        self.publish_status("开始定义区域")
    
    def finish_region_definition(self):
        """完成区域定义"""
        if self.calibration_mode != "DEFINING_REGION":
            print("当前不在定义区域模式")
            return
        
        if len(self.current_region_points) < 2:
            print("至少需要2个点来定义区域")
            return
        
        # 计算区域边界
        x_coords = [p[0] for p in self.current_region_points]
        y_coords = [p[1] for p in self.current_region_points]
        
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        
        # 创建区域
        region_name = f"region_{len(self.defined_regions) + 1}"
        region = {
            "name": region_name,
            "x_range": (x_min, x_max),
            "y_range": (y_min, y_max),
            "type": "visit",
            "points": self.current_region_points.copy()
        }
        
        self.defined_regions.append(region)
        self.calibration_mode = "IDLE"
        
        print(f"区域 {region_name} 定义完成:")
        print(f"  X范围: [{x_min:.2f}, {x_max:.2f}]")
        print(f"  Y范围: [{y_min:.2f}, {y_max:.2f}]")
        print(f"  类型: {region['type']}")
        
        self.publish_status(f"完成区域定义: {region_name}")
    
    def set_current_region_name(self, name: str):
        """设置当前区域名称"""
        if self.defined_regions:
            self.defined_regions[-1]["name"] = name
            print(f"区域名称设置为: {name}")
    
    def set_current_region_type(self, region_type: str):
        """设置当前区域类型"""
        if region_type not in ["visit", "avoid", "stay"]:
            print(f"无效的区域类型: {region_type}")
            return
        
        if self.defined_regions:
            self.defined_regions[-1]["type"] = region_type
            print(f"区域类型设置为: {region_type}")
    
    def clear_all_regions(self):
        """清除所有区域"""
        self.defined_regions = []
        self.current_region_points = []
        self.calibration_mode = "IDLE"
        print("所有区域已清除")
        self.publish_status("所有区域已清除")
    
    def show_current_position(self):
        """显示当前位置"""
        if self.current_position:
            print(f"当前位置: ({self.current_position.x:.2f}, {self.current_position.y:.2f})")
            
            # 检查当前位置在哪些区域内
            for region in self.defined_regions:
                x_min, x_max = region["x_range"]
                y_min, y_max = region["y_range"]
                if (x_min <= self.current_position.x <= x_max and 
                    y_min <= self.current_position.y <= y_max):
                    print(f"  在区域 {region['name']} 内")
        else:
            print("当前位置未知")
    
    def save_configuration(self):
        """保存配置"""
        if not self.defined_regions:
            print("没有定义的区域，无法保存")
            return
        
        # 创建STL配置
        stl_regions = []
        for region_data in self.defined_regions:
            region = STLRegion(
                name=region_data["name"],
                x_range=region_data["x_range"],
                y_range=region_data["y_range"],
                region_type=region_data["type"]
            )
            stl_regions.append(region)
        
        # 创建配置字典
        config = {
            "description": f"标定的STL任务 - {len(stl_regions)}个区域",
            "regions": self.defined_regions,
            "tau": 100,
            "beta": 1.0,
            "threshold": -40.0,
            "calibration_timestamp": rospy.Time.now().to_sec()
        }
        
        # 保存到文件
        filename = f"calibrated_stl_config_{int(rospy.Time.now().to_sec())}.json"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"配置已保存到: {filename}")
            
            # 发布配置
            config_msg = String()
            config_msg.data = json.dumps(config)
            self.config_pub.publish(config_msg)
            
            self.publish_status(f"配置已保存: {filename}")
            
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def publish_status(self, status: str):
        """发布状态"""
        status_data = {
            "status": status,
            "mode": self.calibration_mode,
            "regions_count": len(self.defined_regions),
            "current_region_points": len(self.current_region_points)
        }
        
        msg = String()
        msg.data = json.dumps(status_data)
        self.status_pub.publish(msg)
    
    def publish_markers(self, event):
        """发布可视化标记"""
        marker_array = MarkerArray()
        
        # 清除之前的标记
        delete_marker = Marker()
        delete_marker.action = Marker.DELETEALL
        marker_array.markers.append(delete_marker)
        
        marker_id = 0
        
        # 已定义的区域
        for i, region in enumerate(self.defined_regions):
            # 区域边界框
            marker = Marker()
            marker.header.frame_id = "map"
            marker.header.stamp = rospy.Time.now()
            marker.ns = "stl_regions"
            marker.id = marker_id
            marker.type = Marker.CUBE
            marker.action = Marker.ADD
            
            x_min, x_max = region["x_range"]
            y_min, y_max = region["y_range"]
            
            marker.pose.position.x = (x_min + x_max) / 2
            marker.pose.position.y = (y_min + y_max) / 2
            marker.pose.position.z = 0.1
            marker.pose.orientation.w = 1.0
            
            marker.scale.x = x_max - x_min
            marker.scale.y = y_max - y_min
            marker.scale.z = 0.2
            
            # 根据区域类型设置颜色
            if region["type"] == "visit":
                marker.color.r, marker.color.g, marker.color.b = 0.0, 1.0, 0.0  # 绿色
            elif region["type"] == "avoid":
                marker.color.r, marker.color.g, marker.color.b = 1.0, 0.0, 0.0  # 红色
            else:
                marker.color.r, marker.color.g, marker.color.b = 0.0, 0.0, 1.0  # 蓝色
            
            marker.color.a = 0.3
            marker_array.markers.append(marker)
            marker_id += 1
            
            # 区域名称
            text_marker = Marker()
            text_marker.header.frame_id = "map"
            text_marker.header.stamp = rospy.Time.now()
            text_marker.ns = "region_names"
            text_marker.id = marker_id
            text_marker.type = Marker.TEXT_VIEW_FACING
            text_marker.action = Marker.ADD
            
            text_marker.pose.position.x = (x_min + x_max) / 2
            text_marker.pose.position.y = (y_min + y_max) / 2
            text_marker.pose.position.z = 0.5
            text_marker.pose.orientation.w = 1.0
            
            text_marker.scale.z = 0.3
            text_marker.color.r, text_marker.color.g, text_marker.color.b = 1.0, 1.0, 1.0
            text_marker.color.a = 1.0
            text_marker.text = f"{region['name']} ({region['type']})"
            
            marker_array.markers.append(text_marker)
            marker_id += 1
        
        # 当前正在定义的区域点
        for i, point in enumerate(self.current_region_points):
            marker = Marker()
            marker.header.frame_id = "map"
            marker.header.stamp = rospy.Time.now()
            marker.ns = "current_points"
            marker.id = marker_id
            marker.type = Marker.SPHERE
            marker.action = Marker.ADD
            
            marker.pose.position.x = point[0]
            marker.pose.position.y = point[1]
            marker.pose.position.z = 0.1
            marker.pose.orientation.w = 1.0
            
            marker.scale.x = marker.scale.y = marker.scale.z = 0.2
            marker.color.r, marker.color.g, marker.color.b = 1.0, 1.0, 0.0  # 黄色
            marker.color.a = 1.0
            
            marker_array.markers.append(marker)
            marker_id += 1
        
        self.marker_pub.publish(marker_array)
    
    def run(self):
        """运行标定工具"""
        print("\n=== STL区域标定工具命令 ===")
        print("rostopic pub /calibration_command std_msgs/String 'start_region'")
        print("rostopic pub /calibration_command std_msgs/String 'finish_region'")
        print("rostopic pub /calibration_command std_msgs/String 'set_region_name:区域名称'")
        print("rostopic pub /calibration_command std_msgs/String 'set_region_type:visit'")
        print("rostopic pub /calibration_command std_msgs/String 'save_config'")
        print("rostopic pub /calibration_command std_msgs/String 'clear_all'")
        print("rostopic pub /calibration_command std_msgs/String 'show_current'")
        print("========================\n")
        
        rospy.spin()

if __name__ == "__main__":
    try:
        calibrator = STLRegionCalibrator()
        calibrator.run()
    except rospy.ROSInterruptException:
        print("程序被中断")
    except KeyboardInterrupt:
        print("用户中断程序")
