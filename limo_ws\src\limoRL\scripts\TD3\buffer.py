import numpy as np
 
 
class ReplayBuffer:
    """
    经验回放缓冲区类，用于存储和采样智能体与环境交互产生的经验数据。
    经验回放有助于打破数据之间的相关性，提高深度强化学习算法的稳定性和效率。
    """
    def __init__(self, max_size, state_dim, action_dim, batch_size):
        """
        初始化经验回放缓冲区。

        :param max_size: 缓冲区的最大容量
        :param state_dim: 状态空间的维度
        :param action_dim: 动作空间的维度
        :param batch_size: 采样时的批次大小
        """
        # 缓冲区的最大存储容量
        self.mem_size = max_size
        # 采样时的批次大小
        self.batch_size = batch_size
        # 当前缓冲区中存储的经验数量
        self.mem_cnt = 0

        # 存储状态的内存数组，形状为 (max_size, state_dim)
        self.state_memory = np.zeros((max_size, state_dim))
        # 存储动作的内存数组，形状为 (max_size, action_dim)
        self.action_memory = np.zeros((max_size, action_dim))
        # 存储导航奖励的内存数组，形状为 (max_size, )
        self.nav_reward_memory = np.zeros((max_size, ))
        # 存储STL奖励的内存数组，形状为 (max_size, )
        self.stl_reward_memory = np.zeros((max_size, ))
        # 存储下一个状态的内存数组，形状为 (max_size, state_dim)
        self.next_state_memory = np.zeros((max_size, state_dim))
        # 存储终止标志的内存数组，形状为 (max_size, )，数据类型为布尔型
        self.terminal_memory = np.zeros((max_size, ), dtype=np.bool)
 
    def store_transition(self, state, action, nav_reward, stl_reward, state_, done):
        """
        将智能体与环境交互产生的经验数据存储到缓冲区中。

        :param state: 当前状态
        :param action: 执行的动作
        :param nav_reward: 获得的导航奖励
        :param stl_reward: 获得的STL奖励
        :param state_: 下一个状态
        :param done: 是否终止的标志
        """
        # 计算当前经验数据要存储的位置，使用取模运算实现循环存储
        mem_idx = self.mem_cnt % self.mem_size

        # 将当前状态存储到对应的内存位置
        self.state_memory[mem_idx] = state
        # 将执行的动作存储到对应的内存位置
        self.action_memory[mem_idx] = action
        # 将获得的导航奖励存储到对应的内存位置
        self.nav_reward_memory[mem_idx] = nav_reward
        # 将获得的STL奖励存储到对应的内存位置
        self.stl_reward_memory[mem_idx] = stl_reward
        # 将下一个状态存储到对应的内存位置
        self.next_state_memory[mem_idx] = state_
        # 将终止标志存储到对应的内存位置
        self.terminal_memory[mem_idx] = done

        # 缓冲区中存储的经验数量加 1
        self.mem_cnt += 1
 
    def sample_buffer(self):
        """
        从缓冲区中随机采样一批经验数据。

        :return: 采样得到的状态、动作、导航奖励、STL奖励、下一个状态和终止标志的数组
        """
        # 取当前存储的经验数量和缓冲区最大容量的较小值
        mem_len = min(self.mem_cnt, self.mem_size)
        # 从 [0, mem_len) 范围内随机选择 batch_size 个不重复的索引
        batch = np.random.choice(mem_len, self.batch_size, replace=False)

        # 根据采样的索引获取对应的状态
        states = self.state_memory[batch]
        # 根据采样的索引获取对应的动作
        actions = self.action_memory[batch]
        # 根据采样的索引获取对应的导航奖励
        nav_rewards = self.nav_reward_memory[batch]
        # 根据采样的索引获取对应的STL奖励
        stl_rewards = self.stl_reward_memory[batch]
        # 根据采样的索引获取对应的下一个状态
        states_ = self.next_state_memory[batch]
        # 根据采样的索引获取对应的终止标志
        terminals = self.terminal_memory[batch]

        return states, actions, nav_rewards, stl_rewards, states_, terminals
 
    def ready(self):
        """
        判断缓冲区是否已经存储了足够的经验数据以进行采样。

        :return: 如果存储的经验数量达到或超过批次大小，返回 True；否则返回 False
        """
        return self.mem_cnt >= self.batch_size
