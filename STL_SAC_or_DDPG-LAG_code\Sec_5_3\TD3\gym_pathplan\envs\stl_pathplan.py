import numpy as np
import math

import gym
from gym import error, spaces, utils
from gym.utils import seeding

import sys
import os

# from gym.envs.classic_control import rendering 

class STL_Problem_GF(gym.Env):
    """
    基于 Gym 环境实现的信号时序逻辑（STL）路径规划问题环境类。
    该环境模拟一个机器人在二维空间中移动，需要满足特定的 STL 规范。
    """

    # 定义渲染模式，支持人类可视化和生成 RGB 图像数组
    metadata = {'render.modes': ['human', 'rgb_array']}

    def __init__(self):
        """
        初始化 STL_Problem_GF 环境。
        """
        # 渲染窗口的最大 x 坐标
        self._max_x_of_window = 5.0
        # 渲染窗口的最大 y 坐标
        self._max_y_of_window = 5.0

        # 动态系统的采样周期
        self.dt = 0.1 
        # 每个回合的最大步数
        self._max_episode_steps = 1000

        # 子 STL 公式的时间范围
        hrz_phi_1 = 99.
        hrz_phi_2 = 99.
        # 取两个子 STL 公式时间范围的最大值
        hrz_phi = max(hrz_phi_1,hrz_phi_2)
        # 子 STL 公式 phi_1 的时间边界
        self.phi_1_timebound = [0.0, hrz_phi_1]
        # 子 STL 公式 phi_2 的时间边界
        self.phi_2_timebound = [0.0, hrz_phi_2]
        # 时间范围加 1，用于存储历史轨迹
        self.tau = int(hrz_phi + 1) 
        self.tau_1 = hrz_phi_1
        self.tau_2 = hrz_phi_2

        # 对数求和指数近似参数
        self.beta = 100.

        # 机器人的半径，单位：米
        self.robot_radius = 0.2 

        # 动作参数
        # 最大线速度，单位：米/秒
        self.max_velocity = 1.0   
        # 最小线速度，单位：米/秒
        self.min_velocity = -1.0  
        # 最小角速度，单位：弧度/秒
        self.min_angular_velocity = math.radians(-90)  
        # 最大角速度，单位：弧度/秒
        self.max_angular_velocity = math.radians(90) 

        # 步数计数器
        self.num_steps = 0

        # 状态空间的上下界，状态包含 [x 坐标, y 坐标, 偏航角]
        self.high = np.array([np.inf, np.inf, np.pi], dtype=np.float32)
        self.low = np.array([-np.inf, -np.inf, -np.pi], dtype=np.float32)
        # 状态维度
        self.car_dim = 3
        # 扩展状态空间的上下界，扩展状态包含 [x 坐标, y 坐标, 偏航角, 标志 1, 标志 2]
        self.low_extended_space = np.array([-np.inf, -np.inf, -np.pi, -0.5 , -0.5], dtype=np.float32)
        self.high_extended_space =  np.array([np.inf, np.inf, np.pi, 0.5 , 0.5], dtype=np.float32)

        # 动作空间的上下界，动作包含 [线速度, 角速度]
        self.action_low  = np.array([self.min_velocity, self.min_angular_velocity]) 
        self.action_high = np.array([self.max_velocity, self.max_angular_velocity]) 

        # 定义动作空间为连续空间
        self.action_space = spaces.Box(low=self.action_low, high=self.action_high, shape =(2,), dtype=np.float32)
        # 定义原始状态空间为连续空间
        self.observation_space = spaces.Box(
            low=self.low,
            high=self.high,
            dtype=np.float32 
        )
        # 定义扩展状态空间为连续空间
        self.extended_state_space = spaces.Box(
            low=self.low_extended_space,
            high=self.high_extended_space,
            dtype=np.float32
        )

        # 设置随机种子
        self.seed()
        # 渲染器，初始化为 None
        self.viewer = None
        # 是否可视化激光雷达数据，初始为 True
        self.vis_lidar = True
        # 步数计数器，初始化为 0
        self.num_steps = 0 

        # ##################
        # 初始状态区域
        self.init_low_x = 0.5
        self.init_low_y = 0.5
        self.init_high_x = 2.5
        self.init_high_y = 2.5

        # 子 STL 公式 phi_1 对应的区域
        self.stl_1_low_x = 3.5
        self.stl_1_low_y = 3.5
        self.stl_1_high_x = 4.5
        self.stl_1_high_y = 4.5

        # 子 STL 公式 phi_2 对应的区域
        self.stl_2_low_x = 3.5
        self.stl_2_low_y = 1.5
        self.stl_2_high_x = 4.5
        self.stl_2_high_y = 2.5

        # 更好的区域，用于计算奖励
        self.better_region_low_x = 0.5
        self.better_region_low_y = 0.5
        self.better_region_high_x = 4.5
        self.better_region_high_y = 4.5
        # ##################

    def reset(self): 
        """
        重置环境，返回初始观察值。

        Returns:
            np.ndarray: 初始观察值。
        """
        # 随机初始化初始状态 [x 坐标, y 坐标, 偏航角]
        init_x = self.np_random.uniform(low=self.init_low_x, high=self.init_high_x)
        init_y = self.np_random.uniform(low=self.init_low_y, high=self.init_high_y)
        init_rad = self.np_random.uniform(low=-np.pi/2, high=np.pi/2)

        # 初始化历史轨迹列表，长度为 tau
        self.past_tau_trajectory = [] 
        for i in range(self.tau): 
            current_state = np.array([init_x, init_y, init_rad])
            self.past_tau_trajectory.append(current_state)

        # 重置步数计数器
        self.num_steps = 0 

        # 初始化机器人的当前状态
        self.state = np.array([init_x, init_y, init_rad]) 

        # 对历史轨迹进行预处理，得到观察值
        self.observation = self.preprocess(self.past_tau_trajectory) 

        # 标记回合未结束
        self.done = False
        # 初始化成功值
        self.success_val = 0.0

        return self.observation

    def step(self, action): 
        """
        在环境中执行一个动作，返回下一个观察值、奖励、STL 奖励、是否结束标志和额外信息。

        Args:
            action (np.ndarray): 要执行的动作，包含 [线速度, 角速度]。

        Returns:
            tuple: (观察值, 奖励, STL 奖励, 是否结束标志, 额外信息)
        """
        # 计算 STL 奖励
        stl_reward = self.STLreward(self.past_tau_trajectory) 
        # 计算低层次奖励
        reward = self.reward(self.state, action) 

        # 加入高斯噪声
        noise_w0 = 0.1*np.random.normal(0,1) 
        noise_w1 = 0.1*np.random.normal(0,1)
        noise_w2 = 0.1*np.random.normal(0,1)

        # 更新机器人的状态
        self.state[0] += (action[0] * math.cos(self.state[2]) + noise_w0) * self.dt
        self.state[1] += (action[0] * math.sin(self.state[2]) + noise_w1) * self.dt
        self.state[2] += (action[1] + noise_w2) * self.dt 

        # 确保偏航角在 [-π, π] 范围内
        if self.state[2] < -np.pi:
            self.state[2] += np.pi * 2.0
        elif math.pi < self.state[2]:
            self.state[2] -= np.pi * 2.0

        # 更新历史轨迹，移除最早的状态，添加当前状态
        self.past_tau_trajectory = self.past_tau_trajectory[1:]
        self.past_tau_trajectory.append(self.state.copy())

        # 对历史轨迹进行预处理，得到下一个观察值
        self.observation = self.preprocess(self.past_tau_trajectory) 

        # 步数计数器加 1
        self.num_steps += 1

        # 判断回合是否结束
        if self.num_steps == self._max_episode_steps:
            return_done = True
            # 回合结束，重置环境
            self.reset() 
        else:
            return_done = False

        return self.observation, reward, stl_reward, return_done, {}

    def preprocess(self, tau_state): 
        """
        对历史轨迹进行预处理，返回预处理后的观察值。

        Args:
            tau_state (list): 历史轨迹列表，包含过去 tau 个时间步的状态。

        Returns:
            np.ndarray: 预处理后的观察值。
        """
        # 检查历史轨迹的长度是否正确
        tau_num = len(tau_state)
        assert tau_num == self.tau, "dim of tau-state is wrong."
        # 初始化观察值数组，长度为 car_dim + 2
        obs = np.zeros(self.car_dim + 2)
        # 计算观察值的前三个元素，即当前状态的 x 坐标、y 坐标和偏航角
        obs[0] = tau_state[tau_num-1][0] - (self._max_x_of_window/2)
        obs[1] = tau_state[tau_num-1][1] - (self._max_y_of_window/2)
        obs[2] = tau_state[tau_num-1][2]

        # 初始化两个标志变量
        f1 = 0.0
        f2 = 0.0 
        for i in range(tau_num):
            # 根据子 STL 公式 phi_1 的鲁棒性更新标志 f1
            if self.subSTL_1_robustness(tau_state[i]) >= 0:
                f1 = 1.0
            else:
                f1 = max(f1 - 1/(float(self.tau_1 + 1)), 0.0)
            # 根据子 STL 公式 phi_2 的鲁棒性更新标志 f2
            if self.subSTL_2_robustness(tau_state[i]) >= 0:
                f2 = 1.0
            else:
                f2 = max(f2 - 1/(float(self.tau_2 + 1)), 0.0)
        # 将标志变量减去 0.5，作为观察值的后两个元素
        obs[3] = f1 - 0.5
        obs[4] = f2 - 0.5
        return obs

    def STLreward(self, tau_state): 
        """
        根据历史轨迹计算 STL 奖励。

        Args:
            tau_state (list): 历史轨迹列表，包含过去 tau 个时间步的状态。

        Returns:
            float: STL 奖励。
        """
        # 初始化子 STL 公式 phi_1 和 phi_2 的鲁棒性值
        phi_1_rob = -500. 
        phi_2_rob = -500.

        for i in range(tau_num):
            # 计算当前时间步的子 STL 公式 phi_1 和 phi_2 的鲁棒性值
            temp_1_rob = self.subSTL_1_robustness(tau_state[i])
            temp_2_rob = self.subSTL_2_robustness(tau_state[i])
            # 取最大值更新鲁棒性值
            phi_1_rob = max(phi_1_rob, temp_1_rob)
            phi_2_rob = max(phi_2_rob, temp_2_rob)

        # 取两个子 STL 公式鲁棒性值的最小值
        return_val = min(phi_1_rob, phi_2_rob) 

        # 根据鲁棒性值计算指示函数值
        if return_val >= 0:
            return_val = 1.0
        else:
            return_val = 0.0

        # 计算 STL 奖励
        stl_reward = -np.exp(-self.beta*return_val)

        return stl_reward

    def reward(self, state, action):
        """
        根据当前状态和动作计算低层次奖励。

        Args:
            state (np.ndarray): 当前状态，包含 [x 坐标, y 坐标, 偏航角]。
            action (np.ndarray): 当前动作，包含 [线速度, 角速度]。

        Returns:
            float: 低层次奖励。
        """
        # 初始化奖励值，惩罚线速度和角速度
        return_val = - action[0]**2
        return_val += - action[1]**2
        # 加上更好区域的奖励
        return_val += self.better_region(state)
        return return_val

    def evaluate_stl_formula(self): 
        """
        评估是否满足 STL 规范，被训练器调用。

        Returns:
            float: 评估结果，1.0 表示满足规范，0.0 表示不满足。
        """
        if self.num_steps >= self.tau-1: 
            # 计算历史轨迹的长度
            tau_num = len(self.past_tau_trajectory)
            # 初始化子 STL 公式 phi_1 和 phi_2 的鲁棒性值
            phi_1_rob = -500. 
            phi_2_rob = -500.

            for i in range(tau_num):
                # 计算当前时间步的子 STL 公式 phi_1 和 phi_2 的鲁棒性值
                temp_1_rob = self.subSTL_1_robustness(self.past_tau_trajectory[i])
                temp_2_rob = self.subSTL_2_robustness(self.past_tau_trajectory[i])
                # 取最大值更新鲁棒性值
                phi_1_rob = max(phi_1_rob, temp_1_rob)
                phi_2_rob = max(phi_2_rob, temp_2_rob)

            # 取两个子 STL 公式鲁棒性值的最小值
            returns = min(phi_1_rob, phi_2_rob) 

            # 根据鲁棒性值判断是否满足规范
            if returns >= 0:
                returns = 1.0
            else:
                returns = 0.0

        else: 
            # 步数小于 tau - 1 时，默认满足规范
            returns = 1.0 

        return returns

    def subSTL_1_robustness(self, state):
        """
        计算子 STL 公式 phi_1 关于当前状态的鲁棒性值。

        Args:
            state (np.ndarray): 当前状态，包含 [x 坐标, y 坐标, 偏航角]。

        Returns:
            float: 子 STL 公式 phi_1 的鲁棒性值。
        """
        # 计算状态的 x 坐标和 y 坐标是否在子 STL 公式 phi_1 对应的区域内
        psi1 = state[0] - self.stl_1_low_x 
        psi2 = self.stl_1_high_x - state[0]
        psi3 = state[1] - self.stl_1_low_y 
        psi4 = self.stl_1_high_y - state[1]
        # 取最小值作为鲁棒性值
        robustness = min(psi1, psi2)
        robustness = min(robustness, psi3)
        robustness = min(robustness, psi4)
        return robustness
    
    def subSTL_2_robustness(self, state):
        """
        计算子 STL 公式 phi_2 关于当前状态的鲁棒性值。

        Args:
            state (np.ndarray): 当前状态，包含 [x 坐标, y 坐标, 偏航角]。

        Returns:
            float: 子 STL 公式 phi_2 的鲁棒性值。
        """
        # 计算状态的 x 坐标和 y 坐标是否在子 STL 公式 phi_2 对应的区域内
        psi1 = state[0] - self.stl_2_low_x 
        psi2 = self.stl_2_high_x - state[0]
        psi3 = state[1] - self.stl_2_low_y 
        psi4 = self.stl_2_high_y - state[1]
        # 取最小值作为鲁棒性值
        robustness = min(psi1, psi2)
        robustness = min(robustness, psi3)
        robustness = min(robustness, psi4)
        return robustness

    def better_region(self,state):
        """
        计算当前状态在更好区域内的奖励。

        Args:
            state (np.ndarray): 当前状态，包含 [x 坐标, y 坐标, 偏航角]。

        Returns:
            float: 更好区域的奖励。
        """
        # 计算状态的 x 坐标和 y 坐标是否在更好区域内
        psi1 = state[0] - self.better_region_low_x  
        psi2 = self.better_region_high_x - state[0]
        psi3 = state[1] - self.better_region_low_y 
        psi4 = self.better_region_high_y - state[1]
        # 取最小值作为奖励
        robustness = min(psi1, psi2)
        robustness = min(robustness, psi3)
        robustness = min(robustness, psi4)
        robustness = min(robustness, 0.0)
        return robustness

    ''' def render(self, mode='human', close=False): 
        # 环境可视化函数，当前被注释掉
        screen_width  = 300
        screen_height = 300

        rate_x = screen_width / self._max_x_of_window
        rate_y = screen_height / self._max_y_of_window 

        rate_init_l = self.init_low_x / self._max_x_of_window
        rate_init_r = self.init_high_x / self._max_x_of_window
        rate_init_t = self.init_high_y / self._max_y_of_window
        rate_init_b = self.init_low_y / self._max_y_of_window
        rate_stl_1_l = self.stl_1_low_x / self._max_x_of_window
        rate_stl_1_r = self.stl_1_high_x / self._max_x_of_window
        rate_stl_1_t = self.stl_1_high_y / self._max_y_of_window
        rate_stl_1_b = self.stl_1_low_y / self._max_y_of_window
        rate_stl_2_l = self.stl_2_low_x / self._max_x_of_window
        rate_stl_2_r = self.stl_2_high_x / self._max_x_of_window
        rate_stl_2_t = self.stl_2_high_y / self._max_y_of_window
        rate_stl_2_b = self.stl_2_low_y / self._max_y_of_window
        rate_better_region_l = self.better_region_low_x / self._max_x_of_window
        rate_better_region_r = self.better_region_high_x / self._max_x_of_window
        rate_better_region_t = self.better_region_high_y / self._max_y_of_window
        rate_better_region_b = self.better_region_low_y / self._max_y_of_window

        if self.viewer is None:
            self.viewer = rendering.Viewer(screen_width, screen_height)


            # base
            b1_l, b1_r, b1_t, b1_b = rate_better_region_l*screen_width, rate_better_region_r*screen_width, rate_better_region_t*screen_height, rate_better_region_b*screen_height  # screen のサイズで与える．
            self.b1 = [(b1_l,b1_b), (b1_l,b1_t), (b1_r,b1_t), (b1_r,b1_b)]
            b1 = rendering.make_polygon(self.b1)
            self.b1trans = rendering.Transform()
            b1.add_attr(self.b1trans)
            b1.set_color(0.8, 0.8, 0.8)
            self.viewer.add_geom(b1)

            # start
            start_l, start_r, start_t, start_b = rate_init_l*screen_width, rate_init_r*screen_width, rate_init_t*screen_height, rate_init_b*screen_height  # screen のサイズで与える．
            self.start_area = [(start_l,start_b), (start_l,start_t), (start_r,start_t), (start_r,start_b)]
            start = rendering.make_polygon(self.start_area)
            self.start_area_trans = rendering.Transform()
            start.add_attr(self.start_area_trans)
            start.set_color(0.8, 0.5, 0.5)
            self.viewer.add_geom(start)

            # goal_1
            g1_l, g1_r, g1_t, g1_b = rate_stl_1_l*screen_width, rate_stl_1_r*screen_width, rate_stl_1_t*screen_height, rate_stl_1_b*screen_height  # screen のサイズで与える．
            self.v1 = [(g1_l,g1_b), (g1_l,g1_t), (g1_r,g1_t), (g1_r,g1_b)]
            g1 = rendering.make_polygon(self.v1)
            self.g1trans = rendering.Transform()
            g1.add_attr(self.g1trans)
            g1.set_color(0.5, 0.5, 0.8)
            self.viewer.add_geom(g1)

            # goal_2
            g2_l, g2_r, g2_t, g2_b = rate_stl_2_l*screen_width, rate_stl_2_r*screen_width, rate_stl_2_t*screen_height, rate_stl_2_b*screen_height  # screen のサイズで与える．
            self.v2 = [(g2_l,g2_b), (g2_l,g2_t), (g2_r,g2_t), (g2_r,g2_b)]
            g2 = rendering.make_polygon(self.v2)
            self.g2trans = rendering.Transform()
            g2.add_attr(self.g2trans)
            g2.set_color(0.5, 0.5, 0.8)
            self.viewer.add_geom(g2)

            head_x = (self.robot_radius) * rate_x
            head_y = 0.0 * rate_y
            tail_left_x = (self.robot_radius*np.cos((5/6)+np.pi)) * rate_x
            tail_left_y = (self.robot_radius*np.sin((5/6)+np.pi)) * rate_y
            tail_right_x = (self.robot_radius*np.cos(-(5/6)+np.pi)) * rate_x
            tail_right_y = (self.robot_radius*np.sin(-(5/6)+np.pi)) * rate_y
            self.car_v = [(head_x,head_y), (tail_left_x,tail_left_y), (tail_right_x,tail_right_y)]
            car = rendering.FilledPolygon(self.car_v)
            self.cartrans = rendering.Transform()
            car.add_attr(self.cartrans)
            car.set_color(1.0, 0.0, 0.0)
            self.viewer.add_geom(car)

        car_x = self.state[0] * rate_x
        car_y = self.state[1] * rate_y
   

        self.cartrans.set_translation(car_x, car_y)
        self.cartrans.set_rotation(self.state[2])

        return self.viewer.render(return_rgb_array = mode=='rgb_array') '''

    def close(self):
        """
        关闭环境渲染器。
        """
        if self.viewer:
            self.viewer.close()
            self.viewer = None

    def seed(self, seed=None):
        """
        设置环境的随机种子。

        Args:
            seed (int, optional): 随机种子，默认为 None。

        Returns:
            list: 包含当前随机种子的列表。
        """
        self.np_random, seed = seeding.np_random(seed)
        return [seed]
