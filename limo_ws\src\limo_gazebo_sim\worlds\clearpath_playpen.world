<?xml version="1.0"?>
<!--
Software License Agreement (BSD)

\file      clearpath_playpen.world
\authors   <PERSON><PERSON><PERSON><PERSON> <<EMAIL>
\copyright Copyright (c) 2015, Clearpath Robotics, Inc., All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that
the following conditions are met:
 * Redistributions of source code must retain the above copyright notice, this list of conditions and the
   following disclaimer.
 * Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the 
   following disclaimer in the documentation and/or other materials provided with the distribution.
 * Neither the name of Clearpath Robotics nor the names of its contributors may be used to endorse or promote
   products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WAR-
RANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, IN-
DIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<sdf version='1.4'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
    </light>
    <model name='ground_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100</mu>
                <mu2>50</mu2>
              </ode>
            </friction>
            <bounce/>
            <contact>
              <ode/>
            </contact>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
    </model>
    <physics type='ode'>
      <max_step_size>0.01</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>100</real_time_update_rate>
      <gravity>0 0 -9.8</gravity>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <model name='jersey_barrier'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-8 -10 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_0'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-3 -10 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_1'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>2 -10 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_2'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>7 -10 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_3'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-7.50675 10 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_4'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-2 10 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_5'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>3 10 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_6'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>8 10 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_7'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>9 -4 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_8'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>9 3 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_9'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-9 -6.56962 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_10'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 -2 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_11'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-9.55113 3 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_12'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>9 -8 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_13'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 7 0 0 -0 0</pose>
    </model>
    <model name='jersey_barrier_14'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-9 7.4858 0 0 -0 0</pose>
    </model>
    <model name='fire_hydrant'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://fire_hydrant/meshes/fire_hydrant.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://fire_hydrant/meshes/fire_hydrant.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>6 -6 0 0 -0 0</pose>
    </model>
    <model name='fire_hydrant_0'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://fire_hydrant/meshes/fire_hydrant.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://fire_hydrant/meshes/fire_hydrant.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-3 4 0 0 -0 0</pose>
    </model>
    <model name='fire_hydrant_1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://fire_hydrant/meshes/fire_hydrant.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://fire_hydrant/meshes/fire_hydrant.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-3 -5 0 0 -0 0</pose>
    </model>
    <model name='Dumpster'>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://dumpster/materials/scripts</uri>
              <uri>model://dumpster/materials/textures</uri>
              <name>Dumpster/Diffuse</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <inertial>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>4 5 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='Dumpster_0'>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://dumpster/materials/scripts</uri>
              <uri>model://dumpster/materials/textures</uri>
              <name>Dumpster/Diffuse</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <inertial>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-4 -1 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='jersey_barrier_15'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://jersey_barrier/meshes/jersey_barrier.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='upright'>
          <pose>0 0 0.5715 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.3063 1.143</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base'>
          <pose>0 0 0.032258 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.8107 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base2'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.65 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='base3'>
          <pose>0 0 0.2 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left-angle'>
          <pose>0 -0.224 0.2401 0.9 -0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right-angle'>
          <pose>0 0.224 0.2401 -0.9 0 0</pose>
          <geometry>
            <box>
              <size>4.06542 0.5 0.064516</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>2.45495 0 0 0 -0 0</pose>
    </model>
    <model name='Construction Cone'>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <scale>10 10 10</scale>
              <uri>model://construction_cone/meshes/construction_cone.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <scale>10 10 10</scale>
              <uri>model://construction_cone/meshes/construction_cone.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <inertial>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-5.59121 -8 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='Construction Barrel'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.4 0 -0 0</pose>
          <mass>500</mass>
          <inertia>
            <ixx>51.2096</ixx>
            <iyy>51.2096</iyy>
            <izz>25</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>7 -2.54643 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='Construction Cone_0'>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <scale>10 10 10</scale>
              <uri>model://construction_cone/meshes/construction_cone.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <scale>10 10 10</scale>
              <uri>model://construction_cone/meshes/construction_cone.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <inertial>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>7 6.55652 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='Construction Barrel_0'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.4 0 -0 0</pose>
          <mass>500</mass>
          <inertia>
            <ixx>51.2096</ixx>
            <iyy>51.2096</iyy>
            <izz>25</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>1.45104 -6.51057 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-0.464139 0 0 0 -0 0</pose>
    </model>
    <model name='Construction Barrel_1'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.4 0 -0 0</pose>
          <mass>500</mass>
          <inertia>
            <ixx>51.2096</ixx>
            <iyy>51.2096</iyy>
            <izz>25</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-6.5081 4 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='Construction Barrel_2'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.4 0 -0 0</pose>
          <mass>500</mass>
          <inertia>
            <ixx>51.2096</ixx>
            <iyy>51.2096</iyy>
            <izz>25</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-6.46924 5 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='Construction Barrel_3'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.4 0 -0 0</pose>
          <mass>500</mass>
          <inertia>
            <ixx>51.2096</ixx>
            <iyy>51.2096</iyy>
            <izz>25</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-6.4302 6 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='Construction Barrel_4'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.4 0 -0 0</pose>
          <mass>500</mass>
          <inertia>
            <ixx>51.2096</ixx>
            <iyy>51.2096</iyy>
            <izz>25</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-6 7 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='Construction Barrel_5'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.4 0 -0 0</pose>
          <mass>500</mass>
          <inertia>
            <ixx>51.2096</ixx>
            <iyy>51.2096</iyy>
            <izz>25</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-5 7 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='Construction Barrel_6'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.4 0 -0 0</pose>
          <mass>500</mass>
          <inertia>
            <ixx>51.2096</ixx>
            <iyy>51.2096</iyy>
            <izz>25</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-4 7 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='Construction Barrel_7'>
      <link name='link'>
        <inertial>
          <pose>0 0 0.4 0 -0 0</pose>
          <mass>500</mass>
          <inertia>
            <ixx>51.2096</ixx>
            <iyy>51.2096</iyy>
            <izz>25</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://construction_barrel/meshes/construction_barrel.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-3 7 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <model name='Construction Cone_1'>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <scale>10 10 10</scale>
              <uri>model://construction_cone/meshes/construction_cone.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <scale>10 10 10</scale>
              <uri>model://construction_cone/meshes/construction_cone.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <inertial>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>8.42887 -5 0 0 -0 0</pose>
      <static>0</static>
    </model>
    <state world_name='default'>
      <sim_time>1298 470000000</sim_time>
      <real_time>1304 307193893</real_time>
      <wall_time>1412023784 735639465</wall_time>
      <model name='Construction Barrel'>
        <pose>7.00006 -2.54635 0.049999 4e-06 -0 0.000401</pose>
        <link name='link'>
          <pose>7.00006 -2.54635 0.049999 4e-06 -0 0.000401</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Construction Barrel_0'>
        <pose>1.45178 -6.51128 0.049999 -1e-06 -3e-06 3.8e-05</pose>
        <link name='link'>
          <pose>1.45178 -6.51128 0.049999 -1e-06 -3e-06 3.8e-05</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Construction Barrel_1'>
        <pose>-6.50482 3.9826 0.049999 4e-06 1e-06 -0.009616</pose>
        <link name='link'>
          <pose>-6.50482 3.9826 0.049999 4e-06 1e-06 -0.009616</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Construction Barrel_2'>
        <pose>-6.45065 4.99639 0.049999 -1e-06 -2e-06 0.064981</pose>
        <link name='link'>
          <pose>-6.45065 4.99639 0.049999 -1e-06 -2e-06 0.064981</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Construction Barrel_3'>
        <pose>-6.40174 6.01109 0.05 -1e-06 -1e-06 -0.120729</pose>
        <link name='link'>
          <pose>-6.40174 6.01109 0.05 -1e-06 -1e-06 -0.120729</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Construction Barrel_4'>
        <pose>-6.00634 7.00879 0.049999 3e-06 -0 -0.04957</pose>
        <link name='link'>
          <pose>-6.00634 7.00879 0.049999 3e-06 -0 -0.04957</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Construction Barrel_5'>
        <pose>-4.87565 7.00813 0.049999 -1e-06 -3e-06 0.146162</pose>
        <link name='link'>
          <pose>-4.87565 7.00813 0.049999 -1e-06 -3e-06 0.146162</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Construction Barrel_6'>
        <pose>-3.85615 7.02809 0.0897 -0.01028 -0.077931 0.017839</pose>
        <link name='link'>
          <pose>-3.85615 7.02809 0.0897 -0.01028 -0.077931 0.017839</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Construction Barrel_7'>
        <pose>-2.95979 6.99802 0.049998 -3e-06 -7e-06 -0.017994</pose>
        <link name='link'>
          <pose>-2.95979 6.99802 0.049998 -3e-06 -7e-06 -0.017994</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Construction Cone'>
        <pose>-5.59121 -8 0.05 0 -1e-06 0</pose>
        <link name='link'>
          <pose>-5.59121 -8 0.05 0 -1e-06 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Construction Cone_0'>
        <pose>6.99685 6.55732 0.05 -8.27181e-25 1e-06 -0.007012</pose>
        <link name='link'>
          <pose>6.99685 6.55732 0.05 -8.27181e-25 1e-06 -0.007012</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Construction Cone_1'>
        <pose>8.42887 -5 0.049999 0 3e-06 0</pose>
        <link name='link'>
          <pose>8.42887 -5 0.049999 0 3e-06 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Dumpster'>
        <pose>2.0181 8.87221 0.050916 -1.05879e-22 -3e-06 -0.188821</pose>
        <link name='link'>
          <pose>2.0181 8.87221 0.050916 -1.05879e-22 -3e-06 -0.188821</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='Dumpster_0'>
        <pose>-2.35048 -4.41043 0.050918 0 -0 2.08003</pose>
        <link name='link'>
          <pose>-2.35048 -4.41043 0.050918 0 -0 2.08003</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='asphalt_plane'>
        <pose>0.076733 -0.110379 0 0 -0 0</pose>
        <link name='link'>
          <pose>0.076733 -0.110379 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='fire_hydrant'>
        <pose>3.18024 -6.51118 0 0 -0 0</pose>
        <link name='link'>
          <pose>3.18024 -6.51118 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='fire_hydrant_0'>
        <pose>-3 4 0 0 -0 0</pose>
        <link name='link'>
          <pose>-3 4 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='fire_hydrant_1'>
        <pose>-7.90686 -2.41363 0 0 -0 0</pose>
        <link name='link'>
          <pose>-7.90686 -2.41363 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='ground_plane'>
        <pose>0 0 0 0 -0 0</pose>
        <link name='link'>
          <pose>0 0 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier'>
        <pose>-8 -10 0 0 -0 0</pose>
        <link name='link'>
          <pose>-8 -10 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_0'>
        <pose>-3 -10 0 0 -0 0</pose>
        <link name='link'>
          <pose>-3 -10 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_1'>
        <pose>2 -10 0 0 -0 0</pose>
        <link name='link'>
          <pose>2 -10 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_10'>
        <pose>-10.0774 -2.27159 0 0 -0 1.56</pose>
        <link name='link'>
          <pose>-10.0774 -2.27159 0 0 -0 1.56</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_11'>
        <pose>-9.77013 2.34791 0 0 -0 1.72</pose>
        <link name='link'>
          <pose>-9.77013 2.34791 0 0 -0 1.72</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_12'>
        <pose>10.0879 -7.62159 0 0 -0 1.56</pose>
        <link name='link'>
          <pose>10.0879 -7.62159 0 0 -0 1.56</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_13'>
        <pose>10 7 0 0 -0 1.48</pose>
        <link name='link'>
          <pose>10 7 0 0 -0 1.48</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_14'>
        <pose>-9.64482 7.05946 0 0 -0 1.44</pose>
        <link name='link'>
          <pose>-9.64482 7.05946 0 0 -0 1.44</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_15'>
        <pose>2.45495 0 0 0 -0 2.36</pose>
        <link name='link'>
          <pose>2.45495 0 0 0 -0 2.36</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_2'>
        <pose>7 -10 0 0 -0 0</pose>
        <link name='link'>
          <pose>7 -10 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_3'>
        <pose>-7.50675 10 0 0 -0 0</pose>
        <link name='link'>
          <pose>-7.50675 10 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_4'>
        <pose>-2 10 0 0 -0 0</pose>
        <link name='link'>
          <pose>-2 10 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_5'>
        <pose>3 10 0 0 -0 0</pose>
        <link name='link'>
          <pose>3 10 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_6'>
        <pose>8 10 0 0 -0 0</pose>
        <link name='link'>
          <pose>8 10 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_7'>
        <pose>9.74902 -2.57759 0 0 -0 1.64</pose>
        <link name='link'>
          <pose>9.74902 -2.57759 0 0 -0 1.64</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_8'>
        <pose>9.70141 2.24369 0 0 -0 1.6</pose>
        <link name='link'>
          <pose>9.70141 2.24369 0 0 -0 1.6</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='jersey_barrier_9'>
        <pose>-10.0607 -6.69856 0 0 0 -1.4</pose>
        <link name='link'>
          <pose>-10.0607 -6.69856 0 0 0 -1.4</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
    </state>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose>16.2337 1.23674 17.6497 0 0.923643 -3.08299</pose>
        <view_controller>orbit</view_controller>
      </camera>
    </gui>
  </world>
</sdf>
