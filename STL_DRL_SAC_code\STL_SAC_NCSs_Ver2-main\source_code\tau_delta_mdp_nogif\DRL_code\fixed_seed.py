import numpy as np
import torch
import random

def fixed_seed_function(seed):
    """
    设置固定的随机数种子，确保代码在不同运行环境下的随机性可复现。

    :param seed: 随机数种子，一个整数，用于初始化各种随机数生成器。
    """
    # 设置 Python 内置 random 模块的随机数种子
    random.seed(seed)
    # 设置 NumPy 的随机数种子，保证 NumPy 生成的随机数可复现
    np.random.seed(seed)
    # 设置 PyTorch 的 CPU 随机数种子，确保 CPU 上的随机操作可复现
    torch.manual_seed(seed)
    # 设置 PyTorch 的所有 GPU 随机数种子，确保 GPU 上的随机操作可复现
    torch.cuda.manual_seed_all(seed)
    # 将 PyTorch 的 cuDNN 后端设置为确定性模式，保证卷积操作结果可复现
    torch.backends.cudnn.deterministic = True
