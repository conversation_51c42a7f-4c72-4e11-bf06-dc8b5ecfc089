#!/usr/bin/env python3

import rospy
import numpy as np
import tf2_ros
import tf2_geometry_msgs
from sensor_msgs.msg import LaserScan
from geometry_msgs.msg import Twist, PoseStamped, TransformStamped
from nav_msgs.msg import Odometry
from std_msgs.msg import Bool, String
import json
import time
from typing import List, Tuple, Dict, Any
from stl_config import STLConfig, STLRegion, STLFormula

class RealLimoEnvironment:
    """
    实物Limo机器人环境接口
    用于将训练好的模型部署到真实的Limo机器人上
    """
    
    def __init__(self, stl_task_name: str = "basic_visit", use_amcl: bool = True):
        """
        初始化实物Limo环境
        
        :param stl_task_name: STL任务名称
        :param use_amcl: 是否使用AMCL定位
        """
        rospy.init_node('real_limo_stl_controller', anonymous=True)
        
        # STL任务配置
        self.load_stl_task(stl_task_name)
        
        # 定位方式
        self.use_amcl = use_amcl
        
        # 机器人状态
        self.position = None
        self.orientation = None
        self.yaw = 0.0
        self.linear_vel = 0.0
        self.angular_vel = 0.0
        
        # 传感器数据
        self.laser_data = None
        self.laser_ranges = []
        
        # STL相关
        self.past_tau_trajectory = []
        self.tau = 100
        self.current_stl_flags = [0.0, 0.0]
        
        # 任务状态
        self.task_completed = False
        self.collision_detected = False
        self.emergency_stop = False
        
        # ROS通信设置
        self.setup_ros_communication()
        
        # TF监听器
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)
        
        # 等待初始化完成
        self.wait_for_initialization()
        
        print("实物Limo环境初始化完成")
        print(f"当前STL任务: {self.stl_config['description']}")
    
    def setup_ros_communication(self):
        """设置ROS通信"""
        # 发布者
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
        self.task_status_pub = rospy.Publisher('/stl_task_status', String, queue_size=1)
        
        # 订阅者
        self.laser_sub = rospy.Subscriber('/scan', LaserScan, self.laser_callback)
        
        if self.use_amcl:
            # 使用AMCL定位
            self.pose_sub = rospy.Subscriber('/amcl_pose', PoseStamped, self.amcl_pose_callback)
        else:
            # 使用里程计
            self.odom_sub = rospy.Subscriber('/odom', Odometry, self.odom_callback)
        
        # 紧急停止订阅
        self.emergency_sub = rospy.Subscriber('/emergency_stop', Bool, self.emergency_callback)
        
        # STL任务更新订阅
        self.task_update_sub = rospy.Subscriber('/stl_task_update', String, self.task_update_callback)
    
    def load_stl_task(self, task_name: str):
        """加载STL任务配置"""
        try:
            self.stl_config = STLConfig.get_task_config(task_name)
            self.stl_regions = self.stl_config["regions"]
            self.stl_formula = self.stl_config["formula"]
            self.tau = self.stl_config["tau"]
            self.beta = self.stl_config["beta"]
            print(f"加载STL任务: {task_name}")
        except Exception as e:
            print(f"加载STL任务失败: {e}")
            # 使用默认配置
            self.stl_config = STLConfig.get_task_config("basic_visit")
            self.stl_regions = self.stl_config["regions"]
            self.stl_formula = self.stl_config["formula"]
    
    def laser_callback(self, data: LaserScan):
        """激光雷达数据回调"""
        self.laser_data = data
        # 处理激光雷达数据，降采样到20个点
        ranges = np.array(data.ranges)
        ranges[np.isinf(ranges)] = data.range_max
        ranges[np.isnan(ranges)] = 0.0
        
        # 降采样到20个点
        step = len(ranges) // 20
        self.laser_ranges = ranges[::step][:20].tolist()
        
        # 检测碰撞
        min_distance = np.min(self.laser_ranges)
        if min_distance < 0.3:  # 30cm碰撞阈值
            self.collision_detected = True
    
    def amcl_pose_callback(self, data: PoseStamped):
        """AMCL位姿回调"""
        self.position = data.pose.position
        self.orientation = data.pose.orientation
        
        # 计算yaw角
        import tf.transformations
        quaternion = (
            data.pose.orientation.x,
            data.pose.orientation.y,
            data.pose.orientation.z,
            data.pose.orientation.w
        )
        euler = tf.transformations.euler_from_quaternion(quaternion)
        self.yaw = euler[2]
    
    def odom_callback(self, data: Odometry):
        """里程计回调"""
        self.position = data.pose.pose.position
        self.orientation = data.pose.pose.orientation
        self.linear_vel = data.twist.twist.linear.x
        self.angular_vel = data.twist.twist.angular.z
        
        # 计算yaw角
        import tf.transformations
        quaternion = (
            data.pose.pose.orientation.x,
            data.pose.pose.orientation.y,
            data.pose.pose.orientation.z,
            data.pose.pose.orientation.w
        )
        euler = tf.transformations.euler_from_quaternion(quaternion)
        self.yaw = euler[2]
    
    def emergency_callback(self, data: Bool):
        """紧急停止回调"""
        self.emergency_stop = data.data
        if self.emergency_stop:
            self.stop_robot()
            print("紧急停止激活!")
    
    def task_update_callback(self, data: String):
        """STL任务更新回调"""
        try:
            task_data = json.loads(data.data)
            if "task_name" in task_data:
                self.load_stl_task(task_data["task_name"])
                print(f"STL任务已更新: {task_data['task_name']}")
            elif "config_file" in task_data:
                self.load_stl_task_from_file(task_data["config_file"])
                print(f"从文件加载STL任务: {task_data['config_file']}")
        except Exception as e:
            print(f"任务更新失败: {e}")
    
    def wait_for_initialization(self):
        """等待传感器数据初始化"""
        print("等待传感器数据...")
        rate = rospy.Rate(10)
        
        while not rospy.is_shutdown():
            if (self.position is not None and 
                self.laser_data is not None and 
                len(self.laser_ranges) > 0):
                print("传感器数据就绪")
                break
            rate.sleep()
    
    def get_state(self, past_action: List[float]) -> np.ndarray:
        """获取当前状态"""
        if self.position is None or len(self.laser_ranges) == 0:
            return np.zeros(30)
        
        # 激光雷达数据 (20维)
        lidar_data = self.laser_ranges.copy()
        
        # 导航信息 (3维)
        # 这里需要根据实际任务设置目标点
        goal_x, goal_y = 5.0, 5.0  # 示例目标点
        distance_to_goal = np.sqrt((goal_x - self.position.x)**2 + (goal_y - self.position.y)**2)
        heading_to_goal = np.arctan2(goal_y - self.position.y, goal_x - self.position.x) - self.yaw
        min_obstacle_distance = min(self.laser_ranges) if self.laser_ranges else 10.0
        
        nav_info = [heading_to_goal, distance_to_goal, min_obstacle_distance]
        
        # 位置信息 (3维)
        position_info = [self.position.x, self.position.y, self.yaw]
        
        # 计算STL标志 (2维)
        self.update_stl_trajectory()
        stl_flags = self.calculate_stl_flags()
        
        # 过去动作 (2维)
        past_action_info = past_action if past_action else [0.0, 0.0]
        
        # 组合状态
        state = lidar_data + nav_info + position_info + stl_flags + past_action_info
        
        return np.array(state, dtype=np.float32)
    
    def update_stl_trajectory(self):
        """更新STL轨迹历史"""
        if self.position is not None:
            current_state = [self.position.x, self.position.y, self.yaw]
            self.past_tau_trajectory.append(current_state)
            
            if len(self.past_tau_trajectory) > self.tau:
                self.past_tau_trajectory.pop(0)
    
    def calculate_stl_flags(self) -> List[float]:
        """计算STL标志位"""
        if len(self.past_tau_trajectory) < 2:
            return [0.0, 0.0]
        
        # 使用STL配置计算
        if self.stl_regions and len(self.stl_regions) >= 2:
            region1_visited = any(
                self.stl_regions[0].is_inside(state[0], state[1]) 
                for state in self.past_tau_trajectory
            )
            region2_visited = any(
                self.stl_regions[1].is_inside(state[0], state[1]) 
                for state in self.past_tau_trajectory
            )
            
            flag1 = 0.5 if region1_visited else -0.5
            flag2 = 0.5 if region2_visited else -0.5
            
            return [flag1, flag2]
        
        return [0.0, 0.0]
    
    def execute_action(self, action: np.ndarray):
        """执行动作"""
        if self.emergency_stop or self.collision_detected:
            self.stop_robot()
            return
        
        # 创建速度命令
        cmd_vel = Twist()
        cmd_vel.linear.x = float(action[0])
        cmd_vel.angular.z = float(action[1])
        
        # 安全限制
        cmd_vel.linear.x = np.clip(cmd_vel.linear.x, 0.0, 0.5)  # 限制最大线速度
        cmd_vel.angular.z = np.clip(cmd_vel.angular.z, -1.0, 1.0)  # 限制最大角速度
        
        # 发布命令
        self.cmd_vel_pub.publish(cmd_vel)
    
    def stop_robot(self):
        """停止机器人"""
        cmd_vel = Twist()
        self.cmd_vel_pub.publish(cmd_vel)
    
    def check_stl_completion(self) -> bool:
        """检查STL任务是否完成"""
        if len(self.past_tau_trajectory) < self.tau:
            return False
        
        if self.stl_formula:
            robustness = self.stl_formula.evaluate_robustness(self.past_tau_trajectory)
            return robustness > 0
        
        return False
    
    def publish_task_status(self):
        """发布任务状态"""
        status = {
            "task_completed": self.task_completed,
            "collision_detected": self.collision_detected,
            "emergency_stop": self.emergency_stop,
            "stl_flags": self.current_stl_flags,
            "position": [self.position.x, self.position.y] if self.position else [0, 0],
            "regions_visited": []
        }
        
        # 检查访问的区域
        if self.position and self.stl_regions:
            for i, region in enumerate(self.stl_regions):
                if region.is_inside(self.position.x, self.position.y):
                    status["regions_visited"].append(i)
        
        status_msg = String()
        status_msg.data = json.dumps(status)
        self.task_status_pub.publish(status_msg)
    
    def load_stl_task_from_file(self, config_file: str):
        """从文件加载STL任务"""
        try:
            self.stl_config = STLConfig.load_from_file(config_file)
            self.stl_regions = self.stl_config["regions"]
            self.stl_formula = self.stl_config["formula"]
            self.tau = self.stl_config["tau"]
            self.beta = self.stl_config["beta"]
            # 重置轨迹历史
            self.past_tau_trajectory = []
            print(f"从文件加载STL任务: {config_file}")
        except Exception as e:
            print(f"从文件加载STL任务失败: {e}")

if __name__ == "__main__":
    try:
        # 创建实物环境
        env = RealLimoEnvironment("basic_visit", use_amcl=True)
        
        # 测试环境
        rate = rospy.Rate(10)
        past_action = [0.0, 0.0]
        
        while not rospy.is_shutdown():
            # 获取状态
            state = env.get_state(past_action)
            print(f"State shape: {state.shape}")
            print(f"Position: ({env.position.x:.2f}, {env.position.y:.2f})")
            print(f"STL flags: {env.calculate_stl_flags()}")
            
            # 发布状态
            env.publish_task_status()
            
            rate.sleep()
            
    except rospy.ROSInterruptException:
        print("程序被中断")
    except KeyboardInterrupt:
        print("用户中断程序")
