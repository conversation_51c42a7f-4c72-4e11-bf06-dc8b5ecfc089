#!/usr/bin/env python3

"""
STL约束配置文件
用于定义和管理不同的STL时序逻辑约束
"""

import numpy as np
from typing import List, Dict, Tuple, Any

class STLRegion:
    """STL区域定义类"""
    def __init__(self, name: str, x_range: Tuple[float, float], y_range: Tuple[float, float], 
                 region_type: str = "visit"):
        self.name = name
        self.x_min, self.x_max = x_range
        self.y_min, self.y_max = y_range
        self.region_type = region_type  # "visit", "avoid", "stay"
    
    def is_inside(self, x: float, y: float) -> bool:
        """检查点是否在区域内"""
        return self.x_min <= x <= self.x_max and self.y_min <= y <= self.y_max
    
    def robustness(self, x: float, y: float) -> float:
        """计算点到区域的鲁棒性距离"""
        if self.region_type == "avoid":
            # 避免区域：在区域内为负，区域外为正
            if self.is_inside(x, y):
                # 在区域内，计算到边界的最小距离（负值）
                dist_to_boundary = min(
                    x - self.x_min, self.x_max - x,
                    y - self.y_min, self.y_max - y
                )
                return -dist_to_boundary
            else:
                # 在区域外，计算到区域的最小距离（正值）
                dx = max(0, self.x_min - x, x - self.x_max)
                dy = max(0, self.y_min - y, y - self.y_max)
                return np.sqrt(dx**2 + dy**2)
        else:
            # 访问区域：在区域内为正，区域外为负
            if self.is_inside(x, y):
                # 在区域内，计算到边界的最小距离（正值）
                return min(
                    x - self.x_min, self.x_max - x,
                    y - self.y_min, self.y_max - y
                )
            else:
                # 在区域外，计算到区域的最小距离（负值）
                dx = max(0, self.x_min - x, x - self.x_max)
                dy = max(0, self.y_min - y, y - self.y_max)
                return -np.sqrt(dx**2 + dy**2)

class STLFormula:
    """STL公式定义类"""
    def __init__(self, name: str, formula_type: str, regions: List[STLRegion], 
                 time_bound: int = None, operator: str = "eventually"):
        self.name = name
        self.formula_type = formula_type  # "atomic", "temporal", "logical"
        self.regions = regions
        self.time_bound = time_bound
        self.operator = operator  # "eventually", "always", "until", "and", "or"
    
    def evaluate_robustness(self, trajectory: List[Tuple[float, float, float]]) -> float:
        """评估轨迹对STL公式的鲁棒性"""
        if self.formula_type == "atomic":
            # 原子公式：单个区域约束
            region = self.regions[0]
            if self.operator == "eventually":
                # ◊φ: 最终访问
                max_rob = -float('inf')
                for x, y, _ in trajectory:
                    rob = region.robustness(x, y)
                    max_rob = max(max_rob, rob)
                return max_rob
            elif self.operator == "always":
                # □φ: 始终满足
                min_rob = float('inf')
                for x, y, _ in trajectory:
                    rob = region.robustness(x, y)
                    min_rob = min(min_rob, rob)
                return min_rob
        
        elif self.formula_type == "logical":
            # 逻辑公式：多个子公式的组合
            if self.operator == "and":
                # φ₁ ∧ φ₂
                rob_values = []
                for region in self.regions:
                    max_rob = -float('inf')
                    for x, y, _ in trajectory:
                        rob = region.robustness(x, y)
                        max_rob = max(max_rob, rob)
                    rob_values.append(max_rob)
                return min(rob_values)
            elif self.operator == "or":
                # φ₁ ∨ φ₂
                rob_values = []
                for region in self.regions:
                    max_rob = -float('inf')
                    for x, y, _ in trajectory:
                        rob = region.robustness(x, y)
                        max_rob = max(max_rob, rob)
                    rob_values.append(max_rob)
                return max(rob_values)
        
        return 0.0

class STLConfig:
    """STL配置管理类"""
    
    # 预定义的STL任务配置
    PREDEFINED_TASKS = {
        "basic_visit": {
            "description": "访问两个指定区域",
            "regions": [
                STLRegion("region1", (3.0, 4.0), (3.0, 4.0), "visit"),
                STLRegion("region2", (3.0, 4.0), (-2.0, -1.0), "visit")
            ],
            "formula": STLFormula("visit_both", "logical", [], operator="and"),
            "tau": 100,
            "beta": 1.0,
            "threshold": -40.0
        },
        
        "sequential_visit": {
            "description": "按顺序访问区域（先A后B）",
            "regions": [
                STLRegion("regionA", (1.0, 2.0), (1.0, 2.0), "visit"),
                STLRegion("regionB", (4.0, 5.0), (4.0, 5.0), "visit")
            ],
            "formula": STLFormula("sequential", "temporal", [], operator="until"),
            "tau": 150,
            "beta": 1.0,
            "threshold": -50.0
        },
        
        "avoid_and_visit": {
            "description": "避开危险区域并访问目标区域",
            "regions": [
                STLRegion("danger", (2.0, 3.0), (2.0, 3.0), "avoid"),
                STLRegion("target", (4.0, 5.0), (1.0, 2.0), "visit")
            ],
            "formula": STLFormula("safe_visit", "logical", [], operator="and"),
            "tau": 120,
            "beta": 2.0,
            "threshold": -30.0
        },
        
        "patrol": {
            "description": "巡逻任务：周期性访问检查点",
            "regions": [
                STLRegion("checkpoint1", (1.0, 1.5), (1.0, 1.5), "visit"),
                STLRegion("checkpoint2", (3.0, 3.5), (3.0, 3.5), "visit"),
                STLRegion("checkpoint3", (5.0, 5.5), (1.0, 1.5), "visit")
            ],
            "formula": STLFormula("patrol", "temporal", [], operator="always"),
            "tau": 200,
            "beta": 1.5,
            "threshold": -60.0
        }
    }
    
    @staticmethod
    def get_task_config(task_name: str) -> Dict[str, Any]:
        """获取预定义任务配置"""
        if task_name not in STLConfig.PREDEFINED_TASKS:
            raise ValueError(f"未知任务: {task_name}")
        return STLConfig.PREDEFINED_TASKS[task_name].copy()
    
    @staticmethod
    def create_custom_task(regions: List[STLRegion], formula: STLFormula, 
                          tau: int = 100, beta: float = 1.0, 
                          threshold: float = -40.0) -> Dict[str, Any]:
        """创建自定义STL任务"""
        return {
            "description": "自定义STL任务",
            "regions": regions,
            "formula": formula,
            "tau": tau,
            "beta": beta,
            "threshold": threshold
        }
    
    @staticmethod
    def load_from_file(config_file: str) -> Dict[str, Any]:
        """从配置文件加载STL任务"""
        import json
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 解析区域定义
            regions = []
            for region_data in config_data.get("regions", []):
                region = STLRegion(
                    name=region_data["name"],
                    x_range=(region_data["x_min"], region_data["x_max"]),
                    y_range=(region_data["y_min"], region_data["y_max"]),
                    region_type=region_data.get("type", "visit")
                )
                regions.append(region)
            
            # 解析公式定义
            formula_data = config_data.get("formula", {})
            formula = STLFormula(
                name=formula_data.get("name", "custom"),
                formula_type=formula_data.get("type", "logical"),
                regions=regions,
                time_bound=formula_data.get("time_bound"),
                operator=formula_data.get("operator", "and")
            )
            
            return {
                "description": config_data.get("description", "从文件加载的任务"),
                "regions": regions,
                "formula": formula,
                "tau": config_data.get("tau", 100),
                "beta": config_data.get("beta", 1.0),
                "threshold": config_data.get("threshold", -40.0)
            }
            
        except Exception as e:
            raise ValueError(f"加载配置文件失败: {e}")
    
    @staticmethod
    def save_to_file(task_config: Dict[str, Any], config_file: str):
        """保存STL任务配置到文件"""
        import json
        
        # 转换为可序列化的格式
        config_data = {
            "description": task_config["description"],
            "regions": [
                {
                    "name": region.name,
                    "x_min": region.x_min,
                    "x_max": region.x_max,
                    "y_min": region.y_min,
                    "y_max": region.y_max,
                    "type": region.region_type
                }
                for region in task_config["regions"]
            ],
            "formula": {
                "name": task_config["formula"].name,
                "type": task_config["formula"].formula_type,
                "operator": task_config["formula"].operator,
                "time_bound": task_config["formula"].time_bound
            },
            "tau": task_config["tau"],
            "beta": task_config["beta"],
            "threshold": task_config["threshold"]
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

# 使用示例
if __name__ == "__main__":
    # 获取预定义任务
    task = STLConfig.get_task_config("basic_visit")
    print(f"任务描述: {task['description']}")
    
    # 创建自定义任务
    custom_regions = [
        STLRegion("home", (0.0, 1.0), (0.0, 1.0), "visit"),
        STLRegion("work", (5.0, 6.0), (5.0, 6.0), "visit")
    ]
    custom_formula = STLFormula("home_work", "logical", custom_regions, operator="and")
    custom_task = STLConfig.create_custom_task(custom_regions, custom_formula)
    
    # 保存配置
    STLConfig.save_to_file(custom_task, "custom_stl_task.json")
