import numpy as np
import torch
from torch import nn

class InitStateBuffer:
    """
    初始化状态缓冲区类，用于存储初始状态并支持随机采样。
    """

    def __init__(self, buffer_size, state_shape, device):
        """
        初始化 InitStateBuffer 类的实例。

        参数:
        buffer_size (int): 缓冲区的最大容量。
        state_shape (tuple): 单个状态的形状。
        device (torch.device): 存储数据的设备，如 'cpu' 或 'cuda'。
        """
        # 当前插入位置的索引
        self._p = 0
        # 当前缓冲区中的元素数量
        self._n = 0
        # 缓冲区的最大容量
        self.buffer_size = buffer_size

        # 初始化一个空的张量来存储状态
        self.states = torch.empty((buffer_size, *state_shape), dtype=torch.float, device=device)

    def append(self, state):
        """
        向缓冲区中添加一个新的状态。

        参数:
        state (np.ndarray): 要添加到缓冲区的状态，类型为 NumPy 数组。
        """
        # 将 NumPy 数组转换为 PyTorch 张量，并复制到当前插入位置
        self.states[self._p].copy_(torch.from_numpy(state))

        # 更新插入位置的索引，使用取模运算实现循环缓冲区
        self._p = (self._p + 1) % self.buffer_size
        # 更新缓冲区中的元素数量，不超过缓冲区的最大容量
        self._n = min(self._n + 1, self.buffer_size)

    def sample(self, batch_size):
        """
        从缓冲区中随机采样一批状态。

        参数:
        batch_size (int): 采样的状态数量。

        返回:
        torch.Tensor: 采样得到的状态张量。
        """
        # 随机生成一批索引，范围从 0 到当前缓冲区中的元素数量
        idxes = np.random.randint(low=0, high=self._n, size=batch_size)
        return (
            # 根据生成的索引从缓冲区中取出对应的状态
            self.states[idxes]
        )
