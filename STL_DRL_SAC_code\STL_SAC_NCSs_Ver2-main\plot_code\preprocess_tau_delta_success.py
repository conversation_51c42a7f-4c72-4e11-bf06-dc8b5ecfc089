import numpy as np 
import matplotlib.pyplot as plt

# 设置 matplotlib 的字体为 DejaVu Serif
plt.rcParams["font.family"] = "DejaVu Serif"   # Font
# 设置 matplotlib 中文字的大小为 10
plt.rcParams["font.size"] = 10                 # Size of word

# 循环 14 次，加载 14 组不同编号的 CSV 文件数据
for i in range(14):
    # 加载未预处理数据的 CSV 文件，跳过第一行表头，使用逗号作为分隔符
    data_nopreprocess = np.loadtxt('tau_delta_nopreprocess/success'+str(i)+'.csv',skiprows=1,delimiter=",")
    # 提取未预处理数据的第二列（索引为 1）
    data_nopreprocess_temp = data_nopreprocess[:,1]
    # 将提取的未预处理数据转换为二维数组
    data_nopreprocess_temp = np.array([data_nopreprocess_temp])
    # 加载经过预处理数据的 CSV 文件，跳过第一行表头，使用逗号作为分隔符
    data_proposed_method = np.loadtxt('tau_delta_preprocess/success'+str(i)+'.csv',skiprows=1,delimiter=",")
    # 提取经过预处理数据的第二列（索引为 1）
    data_proposed_method_temp = data_proposed_method[:,1]
    # 将提取的经过预处理数据转换为二维数组
    data_proposed_method_temp = np.array([data_proposed_method_temp])

    # 矩阵维度为 [学习曲线数量, 步数]
    if i > 0:
        # 如果不是第一次循环，将当前未预处理数据追加到已有的未预处理数据矩阵中
        data_nopreprocess_matrix = np.concatenate([data_nopreprocess_matrix, data_nopreprocess_temp])
        # 如果不是第一次循环，将当前经过预处理数据追加到已有的经过预处理数据矩阵中
        data_proposed_method_matrix = np.concatenate([data_proposed_method_matrix, data_proposed_method_temp])
    else:
        # 第一次循环时，直接将当前未预处理数据赋值给未预处理数据矩阵
        data_nopreprocess_matrix = data_nopreprocess_temp
        # 第一次循环时，直接将当前经过预处理数据赋值给经过预处理数据矩阵
        data_proposed_method_matrix = data_proposed_method_temp

# 获取经过预处理数据的长度，即步数
data_len = len(data_proposed_method_temp[0])
# 打印步数
print(data_len)
# 初始化存储学习步数的列表
steps = []
# 生成学习步数列表，每一步间隔 10000
for i in range(data_len):
    steps.append((i+1)*10000)

# 计算未预处理数据的均值，沿学习曲线数量维度计算
train_nopreprocess_scores_mean = np.mean(data_nopreprocess_matrix, axis=0)
# 计算未预处理数据的标准差，沿学习曲线数量维度计算
train_nopreprocess_scores_std = np.std(data_nopreprocess_matrix, axis=0)
# 计算经过预处理数据的均值，沿学习曲线数量维度计算
train_proposed_method_scores_mean = np.mean(data_proposed_method_matrix, axis=0)
# 计算经过预处理数据的标准差，沿学习曲线数量维度计算
train_proposed_method_scores_std = np.std(data_proposed_method_matrix, axis=0)

# 创建一个新的绘图窗口
plt.figure()
# 设置 x 轴标签为 "Learning Steps"
plt.xlabel("Learning Steps")
# 设置 y 轴标签为 "Success Rate"
plt.ylabel("Success Rate")

# 绘制未预处理数据的均值曲线，颜色为红色，标签为 "Without Preprocessing"
plt.plot(steps, train_nopreprocess_scores_mean,color="r", label="Without Preprocessing")
# 绘制经过预处理数据的均值曲线，颜色为蓝色，标签为 "With Preprocessing"
plt.plot(steps, train_proposed_method_scores_mean,color="b", label="With Preprocessing")

# 绘制未预处理数据的标准差范围，颜色为红色，透明度为 0.15
plt.fill_between(steps, train_nopreprocess_scores_mean - train_nopreprocess_scores_std, train_nopreprocess_scores_mean + train_nopreprocess_scores_std, color="r", alpha=0.15)
# 绘制经过预处理数据的标准差范围，颜色为蓝色，透明度为 0.15
plt.fill_between(steps, train_proposed_method_scores_mean - train_proposed_method_scores_std, train_proposed_method_scores_mean + train_proposed_method_scores_std, color="b", alpha=0.15)

# 设置 x 轴的范围为 0 到 600000
plt.xlim(0, 600000)
# 设置 y 轴的范围为 0 到 1.01
plt.ylim(0., 1.01)
# 在最佳位置显示图例
plt.legend(loc="best")
# 显示网格线
plt.grid()
# 注释掉显示图形的代码，避免图形窗口弹出
#plt.show()
# 将绘制的图形保存为 success_rates_preprocess.png 文件
plt.savefig("success_rates_preprocess.png")
