#!/usr/bin/env python
#################################################################################
# Copyright 2018 ROBOTIS CO., LTD.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#################################################################################

# Authors: <AUTHORS>

# 导入 ROS 的 Python 客户端库
import rospy
# 导入随机数生成模块
import random
# 导入时间处理模块
import time
# 导入操作系统相关功能模块
import os
# 导入数学运算模块
import math
# 从 gazebo_msgs.srv 模块导入生成和删除模型的服务
from gazebo_msgs.srv import SpawnModel, DeleteModel
# 从 gazebo_msgs.msg 模块导入模型状态消息
from gazebo_msgs.msg import ModelStates
# 从 geometry_msgs.msg 模块导入位姿消息
from geometry_msgs.msg import Pose

class Respawn():
    def __init__(self):
        # 获取当前脚本所在的文件路径
        self.modelPath = os.path.dirname(os.path.realpath(__file__))
        # 将路径中的 'scripts/TD3' 替换为 'models/turtlebot3_square/goal_box/model.sdf'
        self.modelPath = self.modelPath.replace('scripts/TD3',
                                                'models/turtlebot3_square/goal_box/model.sdf')
        # 以只读模式打开目标模型文件
        self.f = open(self.modelPath, 'r')
        # 读取模型文件内容
        self.model = self.f.read()
        # 从 ROS 参数服务器获取阶段编号
        self.stage = rospy.get_param('/stage_number')
        # 初始化目标的位姿
        self.goal_position = Pose()
        # 初始化目标的初始 x 坐标
        self.init_goal_x = 0.3
        # 初始化目标的初始 y 坐标
        self.init_goal_y = 0

        # 注释掉的备用初始目标坐标
        # self.init_goal_x = 1.2
        # self.init_goal_y = 1.8

        # 设置目标的初始 x 坐标
        self.goal_position.position.x = self.init_goal_x
        # 设置目标的初始 y 坐标
        self.goal_position.position.y = self.init_goal_y
        # 定义目标模型的名称
        self.modelName = 'goal'
        
        # 定义障碍物的坐标
        self.obstacle_1 = 0.3, 0.3
        self.obstacle_2 = 0.3, -0.3
        self.obstacle_3 = -0.3, 0.3
        self.obstacle_4 = -0.3, -0.3
        # 记录上一次目标的 x 坐标
        self.last_goal_x = self.init_goal_x
        # 记录上一次目标的 y 坐标
        self.last_goal_y = self.init_goal_y
        # 记录上一次的索引
        self.last_index = 0
        # 订阅 gazebo 模型状态话题，回调函数为 checkModel
        self.sub_model = rospy.Subscriber('gazebo/model_states', ModelStates, self.checkModel)
        # 标记是否检测到目标模型
        self.check_model = False
        # 当前索引
        self.index = 0
        # 索引计数
        self.index_num = 0
        # R 值计数
        self.R_num = 1

    def checkModel(self, model):
        """
        检查 Gazebo 中是否存在目标模型。

        :param model: 接收到的 ModelStates 消息
        """
        # 初始标记为未检测到目标模型
        self.check_model = False
        # 遍历所有模型名称
        for i in range(len(model.name)):
            if model.name[i] == "goal":
                # 若检测到目标模型，更新标记
                self.check_model = True
   
    def respawnModel(self):
        """
        在 Gazebo 中重新生成目标模型。
        """
        # print("self.check_model: ",self.check_model)
        while True:
            if not self.check_model:
                # 等待 gazebo 生成 SDF 模型的服务可用
                rospy.wait_for_service('gazebo/spawn_sdf_model')
                # 创建服务代理
                spawn_model_prox = rospy.ServiceProxy('gazebo/spawn_sdf_model', SpawnModel)
                # 调用服务生成目标模型
                spawn_model_prox(self.modelName, self.model, 'robotos_name_space', self.goal_position, "world")
                # 记录目标模型的位置信息
                rospy.loginfo("Goal position : %.1f, %.1f", self.goal_position.position.x,
                              self.goal_position.position.y)
                break
            else:
                # 若模型已存在，提示重新加载 gazebo 与训练脚本
                print("重新加载 gazebo 与 训练脚本")
                pass

    def deleteModel(self):
        """
        在 Gazebo 中删除目标模型。
        """
        while True:
            if self.check_model:
                # 等待 gazebo 删除模型的服务可用
                rospy.wait_for_service('gazebo/delete_model')
                # 创建服务代理
                del_model_prox = rospy.ServiceProxy('gazebo/delete_model', DeleteModel)
                # 调用服务删除目标模型
                del_model_prox(self.modelName)
                break
            else:
                pass

    def getPosition(self, position_check=False, delete=False):
        """
        获取目标的位置，并根据需要删除和重新生成目标模型。

        :param position_check: 是否进行位置检查，默认为 False
        :param delete: 是否删除现有目标模型，默认为 False
        :return: 目标的 x 和 y 坐标
        """
        if delete:
            # 若需要删除，调用删除模型方法
            self.deleteModel()

        if self.stage != 4:
            while position_check:
                # 非第 4 阶段的目标位置列表
                goal_x_list = [1,-1,-1,0, 0]   #x
                goal_y_list = [-1,-1,1,1,0.5]  #y
                
                if self.index == 6:
                    # 若索引达到 6，重置为 0
                    self.index = 0
                    
                # 设置目标的 x 坐标
                self.goal_position.position.x = goal_x_list[self.index]
                # 设置目标的 y 坐标
                self.goal_position.position.y = goal_y_list[self.index]
                
                # 索引加 1
                self.index = self.index + 1
                # 停止位置检查
                position_check = False
        else:
            while position_check:
                # 第 4 阶段的目标位置列表，使用三角函数计算 R 值
                R = 0.3*math.sin(math.pi/2)
                goal_x_list = [0.3, -0.3, 0, 0,R,R,-R,-R]
                goal_y_list = [0, 0, 0.3, -0.3,R,-R,R,-R]
            
                # 随机生成一个 0 到 7 之间的索引
                self.index = random.randrange(0, 8)
                print(self.index, self.last_index)
                if self.last_index == self.index:
                    # 若与上一次索引相同，继续进行位置检查
                    position_check = True
                else:
                    # 若与上一次索引不同，更新上一次索引，停止位置检查
                    self.last_index = self.index
                    position_check = False

                # 设置目标的 x 坐标
                self.goal_position.position.x = goal_x_list[self.index]
                # 设置目标的 y 坐标
                self.goal_position.position.y = goal_y_list[self.index]
                
                # 注释掉的备用目标位置设置
                # goal_x_list = 0.6
                # goal_y_list = 0
                
                # self.goal_position.position.x = goal_x_list
                # self.goal_position.position.y = goal_y_list

        # 休眠 0.5 秒
        time.sleep(0.5)
        # 重新生成目标模型
        self.respawnModel()
        # 记录当前目标的 x 坐标
        self.last_goal_x = self.goal_position.position.x
        # 记录当前目标的 y 坐标
        self.last_goal_y = self.goal_position.position.y

        return self.goal_position.position.x, self.goal_position.position.y
