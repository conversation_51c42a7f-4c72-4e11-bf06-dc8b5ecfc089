#!/usr/bin/env python3

import os
import sys
import subprocess
import argparse
import time
import signal
from typing import List

class RealLimoDeployment:
    """
    实物Limo机器人部署管理器
    """
    
    def __init__(self):
        self.processes = []
        self.deployment_config = {
            "use_amcl": True,
            "control_frequency": 10.0,
            "safety_enabled": True,
            "visualization": True
        }
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n接收到中断信号，正在关闭所有进程...")
        self.cleanup()
        sys.exit(0)
    
    def cleanup(self):
        """清理所有进程"""
        for process in self.processes:
            if process.poll() is None:
                process.terminate()
                time.sleep(1)
                if process.poll() is None:
                    process.kill()
        self.processes.clear()
    
    def run_command(self, command: List[str], wait: bool = False):
        """运行命令"""
        try:
            print(f"运行命令: {' '.join(command)}")
            if wait:
                result = subprocess.run(command, check=True)
                return result
            else:
                process = subprocess.Popen(command)
                self.processes.append(process)
                return process
        except subprocess.CalledProcessError as e:
            print(f"命令执行失败: {e}")
            return None
        except FileNotFoundError:
            print(f"命令未找到: {command[0]}")
            return None
    
    def check_dependencies(self):
        """检查依赖"""
        print("检查依赖...")
        
        # 检查ROS
        try:
            subprocess.run(["roscore", "--help"], 
                         stdout=subprocess.DEVNULL, 
                         stderr=subprocess.DEVNULL, 
                         check=True)
            print("✓ ROS已安装")
        except:
            print("✗ ROS未安装或配置不正确")
            return False
        
        # 检查Python包
        required_packages = ["torch", "numpy", "rospy", "tf2_ros"]
        for package in required_packages:
            try:
                __import__(package)
                print(f"✓ {package}已安装")
            except ImportError:
                print(f"✗ {package}未安装")
                return False
        
        return True
    
    def setup_environment(self):
        """设置环境"""
        print("设置环境...")
        
        # 设置ROS环境变量
        os.environ["ROS_MASTER_URI"] = "http://localhost:11311"
        os.environ["ROS_IP"] = "127.0.0.1"
        
        # 检查工作空间
        workspace_path = os.path.expanduser("~/limo_ws")
        if os.path.exists(workspace_path):
            setup_script = os.path.join(workspace_path, "devel/setup.bash")
            if os.path.exists(setup_script):
                print(f"✓ 工作空间: {workspace_path}")
                return True
        
        print("✗ Limo工作空间未找到")
        return False
    
    def launch_basic_nodes(self):
        """启动基础节点"""
        print("启动基础ROS节点...")
        
        # 启动roscore
        self.run_command(["roscore"])
        time.sleep(3)
        
        # 启动Limo基础驱动（根据实际情况调整）
        # self.run_command(["roslaunch", "limo_base", "limo_base.launch"])
        # time.sleep(2)
        
        # 启动激光雷达
        # self.run_command(["roslaunch", "limo_bringup", "limo_start.launch"])
        # time.sleep(2)
        
        print("基础节点启动完成")
    
    def launch_navigation(self):
        """启动导航系统"""
        if not self.deployment_config["use_amcl"]:
            print("跳过AMCL导航启动")
            return
        
        print("启动导航系统...")
        
        # 启动地图服务器（需要预先建图）
        map_file = "~/limo_ws/src/limo_navigation/maps/my_map.yaml"
        if os.path.exists(os.path.expanduser(map_file)):
            self.run_command(["rosrun", "map_server", "map_server", map_file])
            time.sleep(2)
        else:
            print(f"警告: 地图文件未找到 {map_file}")
        
        # 启动AMCL定位
        # self.run_command(["roslaunch", "limo_navigation", "limo_navigation.launch"])
        # time.sleep(3)
        
        print("导航系统启动完成")
    
    def launch_visualization(self):
        """启动可视化"""
        if not self.deployment_config["visualization"]:
            print("跳过可视化启动")
            return
        
        print("启动可视化...")
        
        # 启动rviz
        rviz_config = "~/limo_ws/src/limo_navigation/rviz/navigation.rviz"
        if os.path.exists(os.path.expanduser(rviz_config)):
            self.run_command(["rosrun", "rviz", "rviz", "-d", rviz_config])
        else:
            self.run_command(["rosrun", "rviz", "rviz"])
        
        time.sleep(2)
        print("可视化启动完成")
    
    def launch_stl_controller(self, model_path: str, stl_task: str):
        """启动STL控制器"""
        print(f"启动STL控制器...")
        print(f"模型路径: {model_path}")
        print(f"STL任务: {stl_task}")
        
        # 构建命令
        script_path = os.path.join(os.path.dirname(__file__), "real_limo_controller.py")
        command = [
            "python3", script_path,
            "--model", model_path,
            "--task", stl_task,
            "--freq", str(self.deployment_config["control_frequency"])
        ]
        
        if self.deployment_config["use_amcl"]:
            command.append("--amcl")
        
        self.run_command(command)
        time.sleep(2)
        print("STL控制器启动完成")
    
    def launch_region_calibrator(self):
        """启动区域标定工具"""
        print("启动STL区域标定工具...")
        
        script_path = os.path.join(os.path.dirname(__file__), "stl_region_calibrator.py")
        self.run_command(["python3", script_path])
        time.sleep(1)
        print("区域标定工具启动完成")
    
    def deploy_full_system(self, model_path: str, stl_task: str):
        """部署完整系统"""
        print("=== 开始部署实物Limo STL系统 ===")
        
        # 检查依赖
        if not self.check_dependencies():
            print("依赖检查失败，退出部署")
            return False
        
        # 设置环境
        if not self.setup_environment():
            print("环境设置失败，退出部署")
            return False
        
        try:
            # 启动基础节点
            self.launch_basic_nodes()
            
            # 启动导航系统
            self.launch_navigation()
            
            # 启动可视化
            self.launch_visualization()
            
            # 启动STL控制器
            self.launch_stl_controller(model_path, stl_task)
            
            print("=== 系统部署完成 ===")
            print("\n使用说明:")
            print("1. 在rviz中设置机器人初始位置 (2D Pose Estimate)")
            print("2. 发布开始命令:")
            print("   rostopic pub /limo_start_stop std_msgs/Bool 'data: true'")
            print("3. 发布停止命令:")
            print("   rostopic pub /limo_start_stop std_msgs/Bool 'data: false'")
            print("4. 监控任务状态:")
            print("   rostopic echo /stl_task_status")
            print("   rostopic echo /limo_control_status")
            print("\n按Ctrl+C退出")
            
            # 等待用户中断
            while True:
                time.sleep(1)
                # 检查进程状态
                active_processes = [p for p in self.processes if p.poll() is None]
                if len(active_processes) < len(self.processes):
                    print("检测到进程退出，重新检查系统状态...")
                    self.processes = active_processes
        
        except KeyboardInterrupt:
            print("\n用户中断部署")
        except Exception as e:
            print(f"部署过程中出错: {e}")
        finally:
            self.cleanup()
        
        return True
    
    def deploy_calibration_only(self):
        """仅部署标定工具"""
        print("=== 启动STL区域标定工具 ===")
        
        try:
            # 启动基础节点
            self.launch_basic_nodes()
            
            # 启动导航系统
            self.launch_navigation()
            
            # 启动可视化
            self.launch_visualization()
            
            # 启动标定工具
            self.launch_region_calibrator()
            
            print("=== 标定工具启动完成 ===")
            print("\n使用说明:")
            print("1. 在rviz中查看地图和机器人位置")
            print("2. 使用标定命令定义STL区域")
            print("3. 详细命令请查看终端输出")
            print("\n按Ctrl+C退出")
            
            # 等待用户中断
            while True:
                time.sleep(1)
        
        except KeyboardInterrupt:
            print("\n用户中断标定")
        finally:
            self.cleanup()

def main():
    parser = argparse.ArgumentParser(description='实物Limo机器人STL系统部署工具')
    parser.add_argument('--mode', choices=['full', 'calibration'], default='full',
                       help='部署模式: full(完整系统) 或 calibration(仅标定工具)')
    parser.add_argument('--model', help='训练好的模型路径 (full模式必需)')
    parser.add_argument('--task', default='basic_visit', help='STL任务名称')
    parser.add_argument('--no-amcl', action='store_true', help='不使用AMCL定位')
    parser.add_argument('--no-viz', action='store_true', help='不启动可视化')
    parser.add_argument('--freq', type=float, default=10.0, help='控制频率(Hz)')
    
    args = parser.parse_args()
    
    # 创建部署管理器
    deployer = RealLimoDeployment()
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, deployer.signal_handler)
    signal.signal(signal.SIGTERM, deployer.signal_handler)
    
    # 配置部署参数
    deployer.deployment_config["use_amcl"] = not args.no_amcl
    deployer.deployment_config["visualization"] = not args.no_viz
    deployer.deployment_config["control_frequency"] = args.freq
    
    if args.mode == "full":
        if not args.model:
            print("错误: full模式需要指定模型路径 (--model)")
            return
        
        if not os.path.exists(args.model):
            print(f"错误: 模型文件不存在 {args.model}")
            return
        
        deployer.deploy_full_system(args.model, args.task)
    
    elif args.mode == "calibration":
        deployer.deploy_calibration_only()

if __name__ == "__main__":
    main()
