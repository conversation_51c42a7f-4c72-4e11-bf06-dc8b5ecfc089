<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/plot_code" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/result_plot" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/source_code" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/tau_delta_nopreprocess" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/tau_delta_preprocess" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/tau_preprocess" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/source_code/tau_delta_mdp_nogif" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/source_code/tau_mdp_nogif" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/source_code/tau_delta_mdp_nogif/DRL_code" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/source_code/tau_delta_mdp_nogif/gym_pathplan" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/source_code/tau_mdp_nogif/DRL_code" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/source_code/tau_mdp_nogif/gym_pathplan" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/source_code/tau_delta_mdp_nogif/gym_pathplan/envs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/STL_SAC_NCSs_Ver2-main/source_code/tau_mdp_nogif/gym_pathplan/envs" isTestSource="false" />
    </content>
    <orderEntry type="jdk" jdkName="Python 3.9 (DL)" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
  <component name="PyDocumentationSettings">
    <option name="format" value="PLAIN" />
    <option name="myDocStringFormat" value="Plain" />
  </component>
</module>