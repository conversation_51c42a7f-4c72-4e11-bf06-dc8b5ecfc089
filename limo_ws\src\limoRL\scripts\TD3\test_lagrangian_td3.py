#!/usr/bin/env python3

import rospy
import numpy as np
import os
import time
from Environment import Env
from lagrangian_td3 import LagrangianTD3

def main():
    rospy.init_node('lagrangian_td3_test')
    
    # 环境初始化
    env = Env()
    
    # 测试参数
    max_episodes = 100
    max_timesteps = 500
    
    # 状态和动作维度
    state_dim = 30
    action_dim = 2
    
    # 网络参数
    actor_fc1_dim = 512
    actor_fc2_dim = 512
    critic_fc1_dim = 512
    critic_fc2_dim = 512
    
    # 学习率（测试时不需要，但初始化需要）
    alpha = 3e-4
    beta = 3e-4
    lr_kappa = 1e-5
    
    # 动作限制
    action_limit_v = 0.8
    action_limit_w = 1.8
    
    # 其他超参数
    gamma = 0.99
    tau = 0.005
    action_noise = 0.1
    policy_noise = 0.2
    policy_noise_clip = 0.5
    delay_time = 2
    max_size = 1000000
    batch_size = 512
    threshold = -40.0
    pretrain_steps = 250000
    
    # 检查点目录
    ckpt_dir = './checkpoints'
    
    # 算法初始化
    agent = LagrangianTD3(
        alpha=alpha,
        beta=beta,
        state_dim=state_dim,
        action_dim=action_dim,
        actor_fc1_dim=actor_fc1_dim,
        actor_fc2_dim=actor_fc2_dim,
        critic_fc1_dim=critic_fc1_dim,
        critic_fc2_dim=critic_fc2_dim,
        ckpt_dir=ckpt_dir,
        action_limit_v=action_limit_v,
        action_limit_w=action_limit_w,
        gamma=gamma,
        tau=tau,
        action_noise=action_noise,
        policy_noise=policy_noise,
        policy_noise_clip=policy_noise_clip,
        delay_time=delay_time,
        max_size=max_size,
        batch_size=batch_size,
        lr_kappa=lr_kappa,
        threshold=threshold,
        pretrain_steps=pretrain_steps
    )
    
    # 加载训练好的模型
    model_episode = input("请输入要加载的模型episode数 (或输入'final'加载最终模型): ")
    try:
        agent.load_models(model_episode)
        print(f"成功加载模型: {model_episode}")
    except Exception as e:
        print(f"加载模型失败: {e}")
        return
    
    # 测试循环
    episode_num = 0
    success_count = 0
    collision_count = 0
    timeout_count = 0
    
    nav_rewards = []
    stl_rewards = []
    episode_lengths = []
    
    print("开始测试拉格朗日TD3算法...")
    print(f"测试回合数: {max_episodes}")
    
    while episode_num < max_episodes:
        episode_timesteps = 0
        episode_nav_reward = 0
        episode_stl_reward = 0
        
        # 重置环境
        state = env.reset()
        past_action = [0.0, 0.0]
        
        print(f"\n--- Episode {episode_num + 1} ---")
        
        for t in range(max_timesteps):
            episode_timesteps += 1
            
            # 选择动作（测试模式，不添加噪声）
            action = agent.choose_action(state, train=False)
            
            # 执行动作
            next_state, nav_reward, stl_reward, done = env.step(action, past_action)
            
            # 更新状态和过去动作
            state = next_state
            past_action = action.copy()
            episode_nav_reward += nav_reward
            episode_stl_reward += stl_reward
            
            # 打印当前状态信息
            if t % 50 == 0:
                current_x = state[23]  # 当前x坐标
                current_y = state[24]  # 当前y坐标
                stl_flag1 = state[26]  # STL标志1
                stl_flag2 = state[27]  # STL标志2
                obstacle_range = state[22]  # 最小障碍物距离
                
                print(f"Step {t:3d}: Pos=({current_x:.2f}, {current_y:.2f}), "
                      f"STL_flags=({stl_flag1:.2f}, {stl_flag2:.2f}), "
                      f"Obstacle={obstacle_range:.2f}, "
                      f"Action=({action[0]:.2f}, {action[1]:.2f})")
            
            if done:
                if env.get_goalbox:
                    success_count += 1
                    print(f"成功到达目标! 步数: {episode_timesteps}")
                else:
                    collision_count += 1
                    print(f"发生碰撞! 步数: {episode_timesteps}")
                break
        
        if not done:
            timeout_count += 1
            print(f"超时! 步数: {episode_timesteps}")
        
        # 记录统计信息
        nav_rewards.append(episode_nav_reward)
        stl_rewards.append(episode_stl_reward)
        episode_lengths.append(episode_timesteps)
        
        episode_num += 1
        
        print(f"Nav Reward: {episode_nav_reward:.2f}, STL Reward: {episode_stl_reward:.2f}")
        
        # 检查ROS是否关闭
        if rospy.is_shutdown():
            break
        
        # 等待一段时间再开始下一回合
        time.sleep(1)
    
    # 打印测试结果统计
    print("\n" + "=" * 60)
    print("测试结果统计:")
    print("=" * 60)
    print(f"总回合数: {episode_num}")
    print(f"成功次数: {success_count} ({success_count/episode_num*100:.1f}%)")
    print(f"碰撞次数: {collision_count} ({collision_count/episode_num*100:.1f}%)")
    print(f"超时次数: {timeout_count} ({timeout_count/episode_num*100:.1f}%)")
    print(f"平均回合长度: {np.mean(episode_lengths):.1f} ± {np.std(episode_lengths):.1f}")
    print(f"平均导航奖励: {np.mean(nav_rewards):.2f} ± {np.std(nav_rewards):.2f}")
    print(f"平均STL奖励: {np.mean(stl_rewards):.2f} ± {np.std(stl_rewards):.2f}")
    
    # 保存测试结果
    results = {
        'success_rate': success_count / episode_num,
        'collision_rate': collision_count / episode_num,
        'timeout_rate': timeout_count / episode_num,
        'avg_episode_length': np.mean(episode_lengths),
        'avg_nav_reward': np.mean(nav_rewards),
        'avg_stl_reward': np.mean(stl_rewards),
        'nav_rewards': nav_rewards,
        'stl_rewards': stl_rewards,
        'episode_lengths': episode_lengths
    }
    
    import pickle
    with open(f'test_results_{model_episode}.pkl', 'wb') as f:
        pickle.dump(results, f)
    print(f"\n测试结果已保存到: test_results_{model_episode}.pkl")

if __name__ == '__main__':
    try:
        main()
    except rospy.ROSInterruptException:
        print("测试被中断")
    except KeyboardInterrupt:
        print("用户中断测试")
