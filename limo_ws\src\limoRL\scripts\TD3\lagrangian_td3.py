#!/usr/bin/env python3

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
from buffer import ReplayBuffer
import random

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print("Lagrangian TD3 device: ", device)

class ActorNetwork(nn.Module):
    def __init__(self, alpha, state_dim, action_dim, fc1_dim, fc2_dim, ACTION_limit_v, ACTION_limit_w):
        super(ActorNetwork, self).__init__()

        self.action_limit_v = ACTION_limit_v
        self.action_limit_w = ACTION_limit_w

        self.fc1 = nn.Linear(state_dim, fc1_dim)
        self.fc2 = nn.Linear(fc1_dim, fc2_dim)
        self.fc3 = nn.Linear(fc2_dim, action_dim)

        self.optimizer = optim.Adam(self.parameters(), lr=alpha)
        self.to(device)

    def forward(self, state):
        x = torch.relu(self.fc1(state))
        x = torch.relu(self.fc2(x))
        action = self.fc3(x)
        action[:, 0] = torch.sigmoid(action[:, 0]) * self.action_limit_v
        action[:, 1] = torch.tanh(action[:, 1]) * self.action_limit_w
        return action

    def save_checkpoint(self, checkpoint_file):
        torch.save(self.state_dict(), checkpoint_file, _use_new_zipfile_serialization=False)

    def load_checkpoint(self, checkpoint_file):
        self.load_state_dict(torch.load(checkpoint_file))


class CriticNetwork(nn.Module):
    def __init__(self, beta, state_dim, action_dim, fc1_dim, fc2_dim):
        super(CriticNetwork, self).__init__()
        self.fc1 = nn.Linear(state_dim + action_dim, fc1_dim)
        self.fc2 = nn.Linear(fc1_dim, fc2_dim)
        self.q1 = nn.Linear(fc2_dim, 1)
        self.q2 = nn.Linear(fc2_dim, 1)

        self.optimizer = optim.Adam(self.parameters(), lr=beta)
        self.to(device)

    def forward(self, state, action):
        x = torch.cat([state, action], dim=-1)
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        q1 = self.q1(x)
        q2 = self.q2(x)
        return q1, q2

    def save_checkpoint(self, checkpoint_file):
        torch.save(self.state_dict(), checkpoint_file, _use_new_zipfile_serialization=False)

    def load_checkpoint(self, checkpoint_file):
        self.load_state_dict(torch.load(checkpoint_file))


class LagrangianTD3:
    def __init__(self, alpha, beta, state_dim, action_dim, actor_fc1_dim, actor_fc2_dim,
                 critic_fc1_dim, critic_fc2_dim, ckpt_dir, action_limit_v, action_limit_w, 
                 gamma=0.99, tau=0.005, action_noise=0.1, policy_noise=0.2, 
                 policy_noise_clip=0.5, delay_time=2, max_size=1000000, batch_size=512,
                 lr_kappa=1e-5, threshold=-40.0, pretrain_steps=250000):
        
        self.gamma = gamma
        self.tau = tau
        self.action_noise = action_noise
        self.policy_noise = policy_noise
        self.policy_noise_clip = policy_noise_clip
        self.delay_time = delay_time
        self.update_time = 0
        self.checkpoint_dir = ckpt_dir
        self.batch_size = batch_size
        self.pretrain_steps = pretrain_steps
        self.threshold = threshold
        
        # 拉格朗日乘数
        self.log_kappa = torch.tensor(0.0, requires_grad=True, device=device)
        self.kappa = self.log_kappa.exp()

        # Actor网络
        self.actor = ActorNetwork(alpha=alpha, state_dim=state_dim, action_dim=action_dim,
                                  fc1_dim=actor_fc1_dim, fc2_dim=actor_fc2_dim,
                                  ACTION_limit_v=action_limit_v, ACTION_limit_w=action_limit_w)
        self.target_actor = ActorNetwork(alpha=alpha, state_dim=state_dim, action_dim=action_dim,
                                         fc1_dim=actor_fc1_dim, fc2_dim=actor_fc2_dim,
                                         ACTION_limit_v=action_limit_v, ACTION_limit_w=action_limit_w)

        # 导航Critic网络
        self.nav_critic = CriticNetwork(beta=beta, state_dim=state_dim, action_dim=action_dim,
                                        fc1_dim=critic_fc1_dim, fc2_dim=critic_fc2_dim)
        self.target_nav_critic = CriticNetwork(beta=beta, state_dim=state_dim, action_dim=action_dim,
                                               fc1_dim=critic_fc1_dim, fc2_dim=critic_fc2_dim)

        # STL Critic网络
        self.stl_critic = CriticNetwork(beta=beta, state_dim=state_dim, action_dim=action_dim,
                                        fc1_dim=critic_fc1_dim, fc2_dim=critic_fc2_dim)
        self.target_stl_critic = CriticNetwork(beta=beta, state_dim=state_dim, action_dim=action_dim,
                                               fc1_dim=critic_fc1_dim, fc2_dim=critic_fc2_dim)

        # 拉格朗日乘数优化器
        self.kappa_optimizer = optim.Adam([self.log_kappa], lr=lr_kappa)

        # 经验回放缓冲区
        self.memory = ReplayBuffer(max_size=max_size, state_dim=state_dim, action_dim=action_dim,
                                   batch_size=batch_size)
        
        # 初始化目标网络
        self.update_network_parameters(tau=1.0)

    def update_network_parameters(self, tau=None):
        if tau is None:
            tau = self.tau

        # 更新Actor目标网络
        for actor_params, target_actor_params in zip(self.actor.parameters(),
                                                     self.target_actor.parameters()):
            target_actor_params.data.copy_(tau * actor_params + (1 - tau) * target_actor_params)

        # 更新导航Critic目标网络
        for nav_critic_params, target_nav_critic_params in zip(self.nav_critic.parameters(),
                                                               self.target_nav_critic.parameters()):
            target_nav_critic_params.data.copy_(tau * nav_critic_params + (1 - tau) * target_nav_critic_params)

        # 更新STL Critic目标网络
        for stl_critic_params, target_stl_critic_params in zip(self.stl_critic.parameters(),
                                                               self.target_stl_critic.parameters()):
            target_stl_critic_params.data.copy_(tau * stl_critic_params + (1 - tau) * target_stl_critic_params)

    def remember(self, state, action, nav_reward, stl_reward, state_, done):
        self.memory.store_transition(state, action, nav_reward, stl_reward, state_, done)

    def choose_action(self, observation, train=True, training_step=0):
        self.actor.eval()
        state = torch.tensor([observation], dtype=torch.float).to(device)
        action = self.actor.forward(state)

        if train:
            # 探索噪声
            noise = torch.tensor(np.random.normal(loc=0.0, scale=self.action_noise),
                                 dtype=torch.float).to(device)
            action[0][0] = torch.clamp(action[0][0] + noise, 0.1, 0.8)
            action[0][1] = torch.clamp(action[0][1] + noise, -1.8, 1.8)

            if random.random() < 0.05:
                action[0][1] = -action[0][1]
        
        self.actor.train()
        return action.squeeze().detach().cpu().numpy()

    def is_pretrain_phase(self, training_step):
        return training_step < self.pretrain_steps

    def learn(self, training_step=0):
        if not self.memory.ready():
            return

        states, actions, nav_rewards, stl_rewards, states_, terminals = self.memory.sample_buffer()
        states_tensor = torch.tensor(states, dtype=torch.float).to(device)
        actions_tensor = torch.tensor(actions, dtype=torch.float).to(device)
        nav_rewards_tensor = torch.tensor(nav_rewards, dtype=torch.float).to(device)
        stl_rewards_tensor = torch.tensor(stl_rewards, dtype=torch.float).to(device)
        next_states_tensor = torch.tensor(states_, dtype=torch.float).to(device)
        terminals_tensor = torch.tensor(terminals).to(device)

        # 更新导航Critic
        self.update_nav_critic(states_tensor, actions_tensor, nav_rewards_tensor,
                               next_states_tensor, terminals_tensor)

        # 更新STL Critic
        self.update_stl_critic(states_tensor, actions_tensor, stl_rewards_tensor,
                               next_states_tensor, terminals_tensor)

        self.update_time += 1

        # 延迟更新Actor
        if self.update_time % self.delay_time == 0:
            if self.is_pretrain_phase(training_step):
                # 预训练阶段：只优化STL约束
                self.update_actor_pretrain(states_tensor)
            else:
                # 微调阶段：结合导航奖励和STL约束
                self.update_actor_finetune(states_tensor)
                self.update_kappa(states_tensor)

            # 软更新目标网络
            self.update_network_parameters()

    def update_nav_critic(self, states, actions, nav_rewards, next_states, terminals):
        with torch.no_grad():
            next_actions = self.target_actor.forward(next_states)
            action_noise = torch.tensor(np.random.normal(loc=0.0, scale=self.policy_noise),
                                        dtype=torch.float).to(device)
            action_noise = torch.clamp(action_noise, -self.policy_noise_clip, self.policy_noise_clip)
            next_actions = torch.clamp(next_actions + action_noise, -1, 1)

            q1_, q2_ = self.target_nav_critic.forward(next_states, next_actions)
            q1_ = q1_.view(-1)
            q2_ = q2_.view(-1)
            q1_[terminals] = 0.0
            q2_[terminals] = 0.0
            critic_val = torch.min(q1_, q2_)
            target = nav_rewards + self.gamma * critic_val

        q1, q2 = self.nav_critic.forward(states, actions)
        q1 = q1.view(-1)
        q2 = q2.view(-1)

        critic1_loss = F.huber_loss(q1, target.detach())
        critic2_loss = F.huber_loss(q2, target.detach())

        self.nav_critic.optimizer.zero_grad()
        (critic1_loss + critic2_loss).backward()
        self.nav_critic.optimizer.step()

    def update_stl_critic(self, states, actions, stl_rewards, next_states, terminals):
        with torch.no_grad():
            next_actions = self.target_actor.forward(next_states)
            action_noise = torch.tensor(np.random.normal(loc=0.0, scale=self.policy_noise),
                                        dtype=torch.float).to(device)
            action_noise = torch.clamp(action_noise, -self.policy_noise_clip, self.policy_noise_clip)
            next_actions = torch.clamp(next_actions + action_noise, -1, 1)

            q1_, q2_ = self.target_stl_critic.forward(next_states, next_actions)
            q1_ = q1_.view(-1)
            q2_ = q2_.view(-1)
            q1_[terminals] = 0.0
            q2_[terminals] = 0.0
            critic_val = torch.min(q1_, q2_)
            target = stl_rewards + self.gamma * critic_val

        q1, q2 = self.stl_critic.forward(states, actions)
        q1 = q1.view(-1)
        q2 = q2.view(-1)

        critic1_loss = F.huber_loss(q1, target.detach())
        critic2_loss = F.huber_loss(q2, target.detach())

        self.stl_critic.optimizer.zero_grad()
        (critic1_loss + critic2_loss).backward()
        self.stl_critic.optimizer.step()

    def update_actor_pretrain(self, states):
        """预训练阶段：只优化STL约束"""
        new_actions = self.actor.forward(states)
        stl_q1, stl_q2 = self.stl_critic.forward(states, new_actions)
        stl_q = torch.min(stl_q1, stl_q2)
        actor_loss = -torch.mean(stl_q)

        self.actor.optimizer.zero_grad()
        actor_loss.backward()
        self.actor.optimizer.step()

    def update_actor_finetune(self, states):
        """微调阶段：结合导航奖励和STL约束"""
        new_actions = self.actor.forward(states)

        nav_q1, nav_q2 = self.nav_critic.forward(states, new_actions)
        nav_q = torch.min(nav_q1, nav_q2)

        stl_q1, stl_q2 = self.stl_critic.forward(states, new_actions)
        stl_q = torch.min(stl_q1, stl_q2)

        # 拉格朗日目标函数
        actor_loss = -torch.mean(nav_q + self.kappa * stl_q)

        self.actor.optimizer.zero_grad()
        actor_loss.backward()
        self.actor.optimizer.step()

    def update_kappa(self, states):
        """更新拉格朗日乘数"""
        new_actions = self.actor.forward(states)
        stl_q1, stl_q2 = self.stl_critic.forward(states, new_actions)
        stl_q = torch.min(stl_q1, stl_q2)

        kappa_loss = self.log_kappa.exp() * (stl_q - self.threshold).detach().mean()

        self.kappa_optimizer.zero_grad()
        kappa_loss.backward()
        self.kappa_optimizer.step()

        self.kappa = self.log_kappa.exp()

    def save_models(self, episode):
        self.actor.save_checkpoint(self.checkpoint_dir + '/LagTD3_actor_{}.pth'.format(episode))
        print('Saving actor network successfully!')
        self.target_actor.save_checkpoint(self.checkpoint_dir + '/LagTD3_target_actor_{}.pth'.format(episode))
        print('Saving target_actor network successfully!')

        self.nav_critic.save_checkpoint(self.checkpoint_dir + '/LagTD3_nav_critic_{}.pth'.format(episode))
        print('Saving nav_critic network successfully!')
        self.target_nav_critic.save_checkpoint(self.checkpoint_dir + '/LagTD3_target_nav_critic_{}.pth'.format(episode))
        print('Saving target nav_critic network successfully!')

        self.stl_critic.save_checkpoint(self.checkpoint_dir + '/LagTD3_stl_critic_{}.pth'.format(episode))
        print('Saving stl_critic network successfully!')
        self.target_stl_critic.save_checkpoint(self.checkpoint_dir + '/LagTD3_target_stl_critic_{}.pth'.format(episode))
        print('Saving target stl_critic network successfully!')

        # 保存拉格朗日乘数
        torch.save(self.log_kappa, self.checkpoint_dir + '/LagTD3_kappa_{}.pth'.format(episode))
        print('Saving kappa parameter successfully!')

    def load_models(self, episode):
        self.actor.load_checkpoint(self.checkpoint_dir + '/LagTD3_actor_{}.pth'.format(episode))
        print('Loading actor network successfully!')
        self.target_actor.load_checkpoint(self.checkpoint_dir + '/LagTD3_target_actor_{}.pth'.format(episode))
        print('Loading target_actor network successfully!')

        self.nav_critic.load_checkpoint(self.checkpoint_dir + '/LagTD3_nav_critic_{}.pth'.format(episode))
        print('Loading nav_critic network successfully!')
        self.target_nav_critic.load_checkpoint(self.checkpoint_dir + '/LagTD3_target_nav_critic_{}.pth'.format(episode))
        print('Loading target nav_critic network successfully!')

        self.stl_critic.load_checkpoint(self.checkpoint_dir + '/LagTD3_stl_critic_{}.pth'.format(episode))
        print('Loading stl_critic network successfully!')
        self.target_stl_critic.load_checkpoint(self.checkpoint_dir + '/LagTD3_target_stl_critic_{}.pth'.format(episode))
        print('Loading target stl_critic network successfully!')

        # 加载拉格朗日乘数
        self.log_kappa = torch.load(self.checkpoint_dir + '/LagTD3_kappa_{}.pth'.format(episode))
        self.kappa = self.log_kappa.exp()
        print('Loading kappa parameter successfully!')
