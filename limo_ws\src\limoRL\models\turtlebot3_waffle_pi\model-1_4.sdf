<?xml version="1.0" ?>
<sdf version="1.4">
  <model name="turtlebot3_waffle_pi">  

    <link name="base">

      <inertial>
        <pose>-0.064 0 0.048 0 0 0</pose>
        <inertia>
          <ixx>0.001</ixx>
          <ixy>0.000</ixy>
          <ixz>0.000</ixz>
          <iyy>0.001</iyy>
          <iyz>0.000</iyz>
          <izz>0.001</izz>
        </inertia>
        <mass>1.0</mass>
      </inertial>

      <collision name="base_collision">
        <pose>-0.064 0 0.048 0 0 0</pose>
        <geometry>
          <box>
            <size>0.265 0.265 0.089</size>
          </box>
        </geometry>
      </collision>

      <visual name="base_visual">
        <pose>-0.064 0 0 0 0 0</pose>
        <geometry>
          <mesh>
            <uri>model://turtlebot3_waffle_pi/meshes/waffle_pi_base.dae</uri>
            <scale>0.001 0.001 0.001</scale>
          </mesh>
        </geometry>
      </visual>

      <collision name="right_caster_collision">
        <pose>-0.177 -0.064 -0.004 0 0 0</pose>
        <geometry>
          <sphere>
            <radius>0.005000</radius>
          </sphere>
        </geometry>
        <surface>
          <friction>
            <ode>
              <mu>100000.0</mu>
              <mu2>100000.0</mu2>
              <fdir1>0 0 0</fdir1>
              <slip1>0</slip1>
              <slip2>0</slip2>
            </ode>
          </friction>
        </surface>
      </collision>

      <collision name="left_caster_collision">
        <pose>-0.177 0.064 -0.004 0 0 0</pose>
        <geometry>
          <sphere>
            <radius>0.005000</radius>
          </sphere>
        </geometry>
        <surface>
          <friction>
            <ode>
              <mu>100000.0</mu>
              <mu2>100000.0</mu2>
              <fdir1>0 0 0</fdir1>
              <slip1>0</slip1>
              <slip2>0</slip2>
            </ode>
          </friction>
        </surface>
      </collision>
    </link>

    <link name="lidar">    
      <inertial>
        <pose>-0.052 0 0.111 0 0 0</pose>
        <inertia>
          <ixx>0.001</ixx>
          <ixy>0.000</ixy>
          <ixz>0.000</ixz>
          <iyy>0.001</iyy>
          <iyz>0.000</iyz>
          <izz>0.001</izz>
        </inertia>
        <mass>0.125</mass>
      </inertial>

      <collision name="lidar_sensor_collision">
        <pose>-0.052 0 0.111 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.0508</radius>
            <length>0.055</length>
          </cylinder>
        </geometry>
      </collision>

      <visual name="lidar_sensor_visual">
        <pose>-0.064 0 0.121 0 0 0</pose>
        <geometry>
          <mesh>
            <uri>model://turtlebot3_waffle_pi/meshes/lds.dae</uri>
            <scale>0.001 0.001 0.001</scale>
          </mesh>
        </geometry>
      </visual>

      <sensor name="hls_lfcd_lds" type="ray">
        <always_on>1</always_on>
        <visualize>0</visualize>
        <pose>-0.064 0 0.121 0 0 0</pose>
        <update_rate>1800</update_rate>
        <ray>
          <scan>
            <horizontal>
              <samples>360</samples>
              <resolution>1.000000</resolution>
              <min_angle>0.000000</min_angle>
              <max_angle>6.280000</max_angle>
            </horizontal>
          </scan>
          <range>
            <min>0.120000</min>
            <max>3.5</max>
            <resolution>0.015000</resolution>
          </range>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>0.01</stddev>
          </noise>
        </ray>
      </sensor>
    </link>

    <link name="image">
      <inertial>
        <pose>-0.069 0 0.104 0 0 0</pose>
        <inertia>
          <ixx>0.001</ixx>
          <ixy>0.000</ixy>
          <ixz>0.000</ixz>
          <iyy>0.001</iyy>
          <iyz>0.000</iyz>
          <izz>0.001</izz>
        </inertia>
        <mass>0.035</mass>
      </inertial>

      <collision name="image_collision">
        <pose>-0.069 0 0.104 0 0 0</pose>
        <geometry>
          <box>
            <size>0.008 0.130 0.022</size>
          </box>
        </geometry>
      </collision>

      <sensor name="raspberry_pi_cam" type="camera">
        <pose>0.076 0 0.094 0 0 0</pose>
        <always_on>1</always_on>
        <visualize>0</visualize>
        <update_rate>30</update_rate>
        <camera>
            <horizontal_fov>1.085595</horizontal_fov>
            <image>
                <width>640</width>
                <height>480</height>
                <format>R8G8B8</format>
            </image>
            <depth_camera></depth_camera>
            <clip>
                <near>0.02</near>
                <far>300</far>
            </clip>
        </camera>
      </sensor>
    </link>

    <link name="left_wheel">

      <inertial>
        <pose>0.0 0.144 0.023 -1.57 0 0</pose>
        <inertia>
          <ixx>0.001</ixx>
          <ixy>0.000</ixy>
          <ixz>0.000</ixz>
          <iyy>0.001</iyy>
          <iyz>0.000</iyz>
          <izz>0.001</izz>
        </inertia>
        <mass>0.1</mass>
      </inertial>

      <collision name="left_wheel_collision">
        <pose>0.0 0.144 0.023 -1.57 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.033</radius>
            <length>0.018</length>
          </cylinder>
        </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100000.0</mu>
                <mu2>100000.0</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0.0</slip1>
                <slip2>0.0</slip2>
              </ode>
          </friction>
        </surface>
      </collision>

      <visual name="left_wheel_visual">
        <pose>0.0 0.144 0.023 0 0 0</pose>
        <geometry>
          <mesh>
            <uri>model://turtlebot3_waffle_pi/meshes/left_tire.dae</uri>
            <scale>0.001 0.001 0.001</scale>
          </mesh>
        </geometry>
      </visual>
    </link>

    <link name="right_wheel">

      <inertial>
        <pose>0.0 -0.144 0.023 -1.57 0 0</pose>
        <inertia>
          <ixx>0.001</ixx>
          <ixy>0.000</ixy>
          <ixz>0.000</ixz>
          <iyy>0.001</iyy>
          <iyz>0.000</iyz>
          <izz>0.001</izz>
        </inertia>
        <mass>0.1</mass>
      </inertial>
    
      <collision name="right_wheel_collision">
        <pose>0.0 -0.144 0.023 -1.57 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.033</radius>
            <length>0.018</length>
          </cylinder>
        </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100000.0</mu>
                <mu2>100000.0</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0.0</slip1>
                <slip2>0.0</slip2>
              </ode>
          </friction>
        </surface>
      </collision>

      <visual name="right_wheel_visual">
        <pose>0.0 -0.144 0.023 0 0 0</pose>
        <geometry>
          <mesh>
            <uri>model://turtlebot3_waffle_pi/meshes/right_tire.dae</uri>
            <scale>0.001 0.001 0.001</scale>
          </mesh>
        </geometry>
      </visual>
    </link>

    <joint name="left_wheel_joint" type="revolute">
      <parent>base</parent>
      <child>left_wheel</child>
      <pose>0.0 0.144 0.023 -1.57 0 0</pose>
      <axis>
        <xyz>0 1 0</xyz>
      </axis>
    </joint>

    <joint name="right_wheel_joint" type="revolute">
      <parent>base</parent>
      <child>right_wheel</child>
      <pose>0.0 -0.144 0.023 -1.57 0 0</pose>
      <axis>
        <xyz>0 1 0</xyz>
      </axis>
    </joint>

    <joint name="lidar_joint" type="fixed">
      <parent>base</parent>
      <child>lidar</child>
      <pose>-0.064 0 0.121 0 0 0</pose>
      <axis>
        <xyz>0 0 1</xyz>
        <use_parent_model_frame>true</use_parent_model_frame>
      </axis>
    </joint>

    <joint name="image_joint" type="fixed">
      <parent>base</parent>
      <child>image</child>
      <pose>0.064 -0.065 0.094 0 0 0</pose>
      <axis>
        <xyz>0 0 1</xyz>
        <use_parent_model_frame>true</use_parent_model_frame>
      </axis>
    </joint>

  </model>
</sdf>
