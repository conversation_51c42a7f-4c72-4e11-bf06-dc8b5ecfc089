#!/usr/bin/env python
import torch                                   
import torch.nn as nn                           
import torch.nn.functional as F                 
import numpy as np                                                               
import math, random
from DQN.environment import Env
from DQN.DQNNet import Net, DQN
import time
import rospy
from std_msgs.msg import Float32MultiArray
import os
import sys
from utils import plotLearning

sys.path.append(os.path.dirname(os.path.abspath(os.path.dirname(__file__))))


# 超参数
BATCH_SIZE = 512                                
N_ACTIONS = 5
env =Env(N_ACTIONS)   

if __name__=='__main__':

    dqn = DQN()                                                             
    rospy.init_node('limo_dqn')
    pub_result = rospy.Publisher('result', Float32MultiArray, queue_size=5)
    pub_get_action = rospy.Publisher('get_action', Float32MultiArray, queue_size=5)
    result = Float32MultiArray()
    get_action = Float32MultiArray()     
    start_time =time.time()
    e=dqn.start_epoch
    score_history = []
    save_figure_path = "/home/<USER>/limo_ws/src/limoRL/train/DQN/png/"
    past_action = 0

    for e in range(dqn.load_ep+1, 10000):
                                                                            # 400个episode循环
        s = env.reset(past_action)                                                     # 重置环境
        episode_reward_sum = 0                                              # 初始化该循环对应的episode的总奖励
        done=False
        episode_step=6000
        
      
        for t in range(episode_step):                                       # 开始一个episode (每一个循环代表一步)
            a = dqn.choose_action(s)                                        # 输入该步对应的状态s，选择动作
            s_, r, done = env.step(a,past_action)                                       # 执行动作，获得反馈

            dqn.store_transition(s, a, r, s_)                               # 存储样本
            episode_reward_sum += r                                         # 逐步加上一个episode内每个step的reward
            s = s_                                                          # 更新状态
            pub_get_action.publish(get_action)
            if dqn.memory_counter > BATCH_SIZE:                             # 如果累计的transition数量超过了记忆库的固定容量2000
                dqn.learn()
            if e % 50 ==0:
                dqn.save_model(str(e))
            if t >=2500:
                rospy.loginfo("time out!")
                done =True
            past_action = env.Getang(a)

            print("ep ",e)
            print("done ",done)
            print("reward ",r)
            print("action ",a)
            print("state ",s_)
            print("reward_sum ",episode_reward_sum)
            print("\n")


            if done:                      
                # result.data =[episode_reward_sum,float(dqn.loss),float(dqn.q_eval),float(dqn.q_target)]
                # pub_result.publish(result)
                # m,s =divmod(int(time.time()- start_time),60)
                # h,m =divmod(m,60)
                # rospy.loginfo('Ep: %d score: %.2f memory: %d epsilon: %.2f time: %d:%02d:%02d',e, episode_reward_sum, dqn.memory_counter, dqn.epsilon, h, m, s)
                # param_keys = ['epsilon']
                # param_values = [dqn.epsilon]
                # param_dictionary = dict(zip(param_keys, param_values))
                break       

            if dqn.epsilon > dqn.epsilon_min :
                dqn.epsilon =dqn.epsilon-0.0001

        score_history.append(episode_reward_sum)
        if e % 2 == 0:
            filename_score = save_figure_path + "score_history_" + str(e) + ".png"
            plotLearning(score_history, filename_score, window=100)
            dqn.save_model(e=e)
            with open(save_figure_path +'score_history.txt', 'w') as f:
                for score in score_history:
                    f.write(str(score) + '\n')
