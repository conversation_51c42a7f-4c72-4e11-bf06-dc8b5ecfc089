import math
import torch

def calculate_log_pi(log_stds, noises, actions):
    """
    计算随机动作的对数概率密度。

    :param log_stds: 对数标准差张量，用于描述动作分布的标准差。
    :param noises: 从标准正态分布采样得到的噪声张量。
    :param actions: 通过重参数化技巧得到并经过双曲正切函数处理后的动作张量。
    :return: 随机动作的对数概率密度张量。
    """
    # 计算高斯分布下的对数概率。
    # 公式依据高斯分布的概率密度函数的对数形式：
    # log_prob = -0.5 * noise^2 - log_std - 0.5 * log(2 * pi) * num_dimensions
    # 其中，num_dimensions 是最后一个维度的大小，表示动作的维度
    gaussian_log_probs = \
        (-0.5 * noises.pow(2) - log_stds).sum(dim=-1, keepdim=True) - 0.5 * math.log(2 * math.pi) * log_stds.size(-1)

    # 由于动作经过了 tanh 函数处理，需要对对数概率进行修正。
    # 修正项为 -log(1 - action^2)，加上一个小的常数 1e-6 避免数值不稳定
    log_pis = gaussian_log_probs - torch.log(1 - actions.pow(2) + 1e-6).sum(dim=-1, keepdim=True)

    return log_pis

def reparameterize(means, log_stds):
    """ 重参数化技巧 """
    # 计算标准差，通过对对数标准差取指数得到
    stds = log_stds.exp()
    # 从标准正态分布中采样噪声，噪声的形状与均值张量相同
    noises = torch.randn_like(means)
    # 使用重参数化技巧从正态分布 N(means, stds) 中采样得到样本
    us = means + noises * stds
    # 对采样得到的样本应用双曲正切函数，将动作限制在 [-1, 1] 范围内
    actions = torch.tanh(us)

    # 计算随机动作的对数概率密度
    log_pis = calculate_log_pi(log_stds, noises, actions)

    return actions, log_pis
