import numpy as np

# 代码来源：https://github.com/songrotek/DDPG/blob/master/ou_noise.py
class OU_NOISE:
    """
    Ornstein-Uhlenbeck 噪声类，用于在强化学习中为动作添加噪声，以促进探索。
    Ornstein-Uhlenbeck 过程生成的噪声具有时间相关性，适合连续动作空间的任务。
    """

    def __init__(self, action_dimension, scale=1.0, mu=0, theta=0.15, sigma=0.3):
        """
        初始化 OU 噪声实例。

        参数:
        action_dimension (int): 动作空间的维度。
        scale (float, 可选): 噪声的缩放因子，默认为 1.0。原默认值为 0.3 已注释说明。
        mu (float, 可选): 噪声过程的均值，默认为 0。
        theta (float, 可选): 噪声回归到均值的速率，默认为 0.15。
        sigma (float, 可选): 噪声的标准差，默认为 0.3。
        """
        # 动作空间的维度
        self.action_dimension = action_dimension
        # 噪声的缩放因子
        self.scale = scale
        # 噪声过程的均值
        self.mu = mu
        # 噪声回归到均值的速率
        self.theta = theta
        # 噪声的标准差
        self.sigma = sigma
        # 初始化噪声状态，使其等于均值
        self.state = np.ones(self.action_dimension) * self.mu
        # 调用重置方法，确保初始状态正确
        self.reset()

    def reset(self):
        """
        将噪声状态重置为均值，用于在每个新的训练回合开始时调用。
        """
        self.state = np.ones(self.action_dimension) * self.mu

    def noise(self):
        """
        生成一个新的 OU 噪声样本。

        返回:
        np.ndarray: 缩放后的噪声样本，形状与动作空间维度相同。
        """
        # 获取当前噪声状态
        x = self.state
        # 根据 Ornstein-Uhlenbeck 过程的公式计算状态的变化量
        dx = self.theta * (self.mu - x) + self.sigma * np.random.randn(len(x))
        # 更新噪声状态
        self.state = x + dx
        # 返回缩放后的噪声状态
        return self.state * self.scale
