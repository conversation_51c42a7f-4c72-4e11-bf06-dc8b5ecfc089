<?xml version="1.0"?>
<package format="2">
  <name>limo_bringup</name>
  <version>0.0.0</version>
  <description>The limo_bringup package</description>
  <maintainer email="<EMAIL>">agilex</maintainer>
  <license>BSD 3-Clause</license>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>controller_manager</build_depend>
  <build_depend>joint_state_controller</build_depend>
  <build_depend>robot_state_publisher</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>move_base_msgs</build_depend>
  <build_depend>actionlib</build_depend>
  <build_depend>tf</build_depend>
  <build_depend>cv_bridge</build_depend>
  <build_depend>nav_msgs</build_depend>

  <build_export_depend>controller_manager</build_export_depend>
  <build_export_depend>joint_state_controller</build_export_depend>
  <build_export_depend>robot_state_publisher</build_export_depend>
  <build_export_depend>roscpp</build_export_depend>
  <build_export_depend>rospy</build_export_depend>
  <build_export_depend>std_msgs</build_export_depend>
  <build_export_depend>geometry_msgs</build_export_depend>
  <build_export_depend>move_base_msgs</build_export_depend>
  <build_export_depend>actionlib</build_export_depend>
  <build_export_depend>tf</build_export_depend>
  <build_export_depend>cv_bridge</build_export_depend>
  <build_export_depend>nav_msgs</build_export_depend>


  <exec_depend>controller_manager</exec_depend>
  <exec_depend>joint_state_controller</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>
  <exec_depend>roscpp</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>move_base_msgs</exec_depend>
  <exec_depend>actionlib</exec_depend>
  <exec_depend>tf</exec_depend>
  <exec_depend>cv_bridge</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  
</package>
