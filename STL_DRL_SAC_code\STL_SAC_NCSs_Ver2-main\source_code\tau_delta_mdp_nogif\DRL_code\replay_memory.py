import numpy as np
import torch
from torch import nn

class ReplayBuffer:
    """
    经验回放缓冲区类，用于存储智能体与环境交互产生的经验数据，并从中随机采样。
    经验回放可以打破数据之间的相关性，提高深度强化学习算法的稳定性和效率。
    """

    def __init__(self, buffer_size, state_shape, action_shape, device):
        """
        初始化经验回放缓冲区。

        :param buffer_size: 缓冲区的最大容量，即最多能存储的经验数据数量。
        :param state_shape: 状态的形状，用于确定存储状态数据的张量维度。
        :param action_shape: 动作的形状，用于确定存储动作数据的张量维度。
        :param device: 数据存储的设备，如 'cpu' 或 'cuda'。
        """
        # 当前要插入数据的位置索引
        self._p = 0
        # 当前缓冲区中存储的经验数据数量
        self._n = 0
        # 缓冲区的最大容量
        self.buffer_size = buffer_size

        # 存储状态数据的张量，形状为 (buffer_size, *state_shape)
        self.states = torch.empty((buffer_size, *state_shape), dtype=torch.float, device=device)
        # 存储动作数据的张量，形状为 (buffer_size, *action_shape)
        self.actions = torch.empty((buffer_size, *action_shape), dtype=torch.float, device=device)
        # 存储奖励数据的张量，形状为 (buffer_size, 1)
        self.rewards = torch.empty((buffer_size, 1), dtype=torch.float, device=device)
        # 存储回合结束标志数据的张量，形状为 (buffer_size, 1)
        self.dones = torch.empty((buffer_size, 1), dtype=torch.float, device=device)
        # 存储下一个状态数据的张量，形状为 (buffer_size, *state_shape)
        self.next_states = torch.empty((buffer_size, *state_shape), dtype=torch.float, device=device)

    def append(self, state, action, reward, done, next_state):
        """
        向缓冲区中添加一组新的经验数据。

        :param state: 当前状态，通常为 NumPy 数组。
        :param action: 当前采取的动作，通常为 NumPy 数组。
        :param reward: 执行动作后获得的奖励。
        :param done: 表示当前回合是否结束的布尔值。
        :param next_state: 执行动作后转移到的下一个状态，通常为 NumPy 数组。
        """
        # 将 NumPy 数组转换为 PyTorch 张量，并复制到缓冲区的当前插入位置
        self.states[self._p].copy_(torch.from_numpy(state))
        # 将 NumPy 数组转换为 PyTorch 张量，并复制到缓冲区的当前插入位置
        self.actions[self._p].copy_(torch.from_numpy(action))
        # 将奖励值转换为浮点数并存储到缓冲区的当前插入位置
        self.rewards[self._p] = float(reward)
        # 将回合结束标志转换为浮点数并存储到缓冲区的当前插入位置
        self.dones[self._p] = float(done)
        # 将 NumPy 数组转换为 PyTorch 张量，并复制到缓冲区的当前插入位置
        self.next_states[self._p].copy_(torch.from_numpy(next_state))

        # 更新插入位置索引，使用取模运算实现循环覆盖
        self._p = (self._p + 1) % self.buffer_size
        # 更新当前缓冲区中存储的经验数据数量，不超过缓冲区最大容量
        self._n = min(self._n + 1, self.buffer_size)

    def sample(self, batch_size):
        """
        从缓冲区中随机采样一批经验数据。

        :param batch_size: 采样的经验数据批次大小。
        :return: 包含采样的状态、动作、奖励、回合结束标志和下一个状态的元组。
        """
        # 随机生成一批索引，范围从 0 到当前缓冲区中存储的经验数据数量
        idxes = np.random.randint(low=0, high=self._n, size=batch_size)
        return (
            self.states[idxes],  # 采样的状态数据
            self.actions[idxes], # 采样的动作数据
            self.rewards[idxes], # 采样的奖励数据
            self.dones[idxes],   # 采样的回合结束标志数据
            self.next_states[idxes] # 采样的下一个状态数据
        )
