#!/usr/bin/env python3

import rospy
import numpy as np
import os
import time
from Environment import Env
from lagrangian_td3 import LagrangianTD3

def main():
    rospy.init_node('lagrangian_td3_train')
    
    # 环境初始化
    env = Env()
    
    # 训练参数
    max_episodes = 10000
    max_timesteps = 500
    start_timesteps = 25000  # 开始学习前的随机探索步数
    pretrain_steps = 250000  # 预训练步数
    
    # 状态和动作维度
    # 新的状态空间: [激光雷达(20) + 导航(3) + 位置(3) + STL标志(2) + 过去动作(2)] = 30维
    state_dim = 30
    action_dim = 2
    
    # 网络参数
    actor_fc1_dim = 512
    actor_fc2_dim = 512
    critic_fc1_dim = 512
    critic_fc2_dim = 512
    
    # 学习率
    alpha = 3e-4  # Actor学习率
    beta = 3e-4   # Critic学习率
    lr_kappa = 1e-5  # 拉格朗日乘数学习率
    
    # 动作限制
    action_limit_v = 0.8  # 线速度限制
    action_limit_w = 1.8  # 角速度限制
    
    # 其他超参数
    gamma = 0.99
    tau = 0.005
    action_noise = 0.1
    policy_noise = 0.2
    policy_noise_clip = 0.5
    delay_time = 2
    max_size = 1000000
    batch_size = 512
    threshold = -40.0  # STL约束阈值
    
    # 检查点目录
    ckpt_dir = './checkpoints'
    if not os.path.exists(ckpt_dir):
        os.makedirs(ckpt_dir)
    
    # 算法初始化
    agent = LagrangianTD3(
        alpha=alpha,
        beta=beta,
        state_dim=state_dim,
        action_dim=action_dim,
        actor_fc1_dim=actor_fc1_dim,
        actor_fc2_dim=actor_fc2_dim,
        critic_fc1_dim=critic_fc1_dim,
        critic_fc2_dim=critic_fc2_dim,
        ckpt_dir=ckpt_dir,
        action_limit_v=action_limit_v,
        action_limit_w=action_limit_w,
        gamma=gamma,
        tau=tau,
        action_noise=action_noise,
        policy_noise=policy_noise,
        policy_noise_clip=policy_noise_clip,
        delay_time=delay_time,
        max_size=max_size,
        batch_size=batch_size,
        lr_kappa=lr_kappa,
        threshold=threshold,
        pretrain_steps=pretrain_steps
    )
    
    # 训练循环
    total_timesteps = 0
    episode_num = 0
    
    print("开始训练拉格朗日TD3算法...")
    print(f"预训练步数: {pretrain_steps}")
    print(f"状态维度: {state_dim}")
    print(f"动作维度: {action_dim}")
    
    while episode_num < max_episodes:
        episode_timesteps = 0
        episode_nav_reward = 0
        episode_stl_reward = 0
        
        # 重置环境
        state = env.reset()
        past_action = [0.0, 0.0]  # 初始化过去动作
        
        for t in range(max_timesteps):
            episode_timesteps += 1
            
            # 选择动作
            if total_timesteps < start_timesteps:
                # 随机探索阶段
                action = np.random.uniform(low=[-action_limit_v, -action_limit_w], 
                                         high=[action_limit_v, action_limit_w])
            else:
                # 使用策略选择动作
                action = agent.choose_action(state, train=True, training_step=total_timesteps)
            
            # 执行动作
            next_state, nav_reward, stl_reward, done = env.step(action, past_action)
            
            # 存储经验
            agent.remember(state, action, nav_reward, stl_reward, next_state, done)
            
            # 更新状态和过去动作
            state = next_state
            past_action = action.copy()
            episode_nav_reward += nav_reward
            episode_stl_reward += stl_reward
            
            # 训练
            if total_timesteps >= start_timesteps:
                agent.learn(training_step=total_timesteps)
            
            total_timesteps += 1
            
            if done:
                break
        
        episode_num += 1
        
        # 打印训练信息
        if episode_num % 10 == 0:
            phase = "预训练" if total_timesteps < pretrain_steps else "微调"
            kappa_value = agent.kappa.item() if hasattr(agent, 'kappa') else 0.0
            
            print(f"Episode: {episode_num:4d}, "
                  f"Steps: {episode_timesteps:3d}, "
                  f"Nav Reward: {episode_nav_reward:8.2f}, "
                  f"STL Reward: {episode_stl_reward:8.2f}, "
                  f"Phase: {phase}, "
                  f"Kappa: {kappa_value:.4f}, "
                  f"Total Steps: {total_timesteps}")
        
        # 阶段转换提示
        if total_timesteps == pretrain_steps:
            print("=" * 60)
            print("预训练阶段结束，开始微调阶段")
            print("=" * 60)
        
        # 保存模型
        if episode_num % 1000 == 0:
            agent.save_models(episode_num)
            print(f"模型已保存 (Episode {episode_num})")
        
        # 检查ROS是否关闭
        if rospy.is_shutdown():
            break
    
    print("训练完成!")
    agent.save_models("final")

if __name__ == '__main__':
    try:
        main()
    except rospy.ROSInterruptException:
        print("训练被中断")
    except KeyboardInterrupt:
        print("用户中断训练")
