<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/Sec_5_1_1" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/Sec_5_1_1/no_pre_train" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/Sec_5_1_1/no_pre_train/DeepRL_code" isTestSource="false" />
    </content>
    <orderEntry type="jdk" jdkName="Python 3.9 (DL)" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
  <component name="PyDocumentationSettings">
    <option name="format" value="PLAIN" />
    <option name="myDocStringFormat" value="Plain" />
  </component>
  <component name="TestRunnerService">
    <option name="PROJECT_TEST_RUNNER" value="pytest" />
  </component>
</module>