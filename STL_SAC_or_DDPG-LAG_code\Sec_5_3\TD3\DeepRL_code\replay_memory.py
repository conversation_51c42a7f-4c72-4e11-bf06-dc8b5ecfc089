import numpy as np
import torch
from torch import nn

class ReplayBuffer:
    """
    经验回放缓冲区类，用于存储智能体在环境中交互的经验数据。
    经验回放机制有助于打破数据之间的相关性，提高强化学习算法的稳定性和效率。
    """

    def __init__(self, buffer_size, state_shape, action_shape, device):
        """
        初始化经验回放缓冲区。

        参数:
        buffer_size (int): 缓冲区的最大容量。
        state_shape (tuple): 单个状态的形状。
        action_shape (tuple): 单个动作的形状。
        device (torch.device): 数据存储的设备，如 'cpu' 或 'cuda'。
        """
        # 当前插入位置的索引
        self._p = 0
        # 当前缓冲区中存储的经验数量
        self._n = 0
        # 缓冲区的最大容量
        self.buffer_size = buffer_size

        # 用于存储状态的张量
        self.states = torch.empty((buffer_size, *state_shape), dtype=torch.float, device=device)
        # 用于存储动作的张量
        self.actions = torch.empty((buffer_size, *action_shape), dtype=torch.float, device=device)
        # 用于存储奖励的张量
        self.rewards = torch.empty((buffer_size, 1), dtype=torch.float, device=device)
        # 用于存储代价的张量
        self.costs = torch.empty((buffer_size, 1), dtype=torch.float, device=device)
        # 用于存储回合结束标志的张量
        self.dones = torch.empty((buffer_size, 1), dtype=torch.float, device=device)
        # 用于存储下一个状态的张量
        self.next_states = torch.empty((buffer_size, *state_shape), dtype=torch.float, device=device)

    def append(self, state, action, reward, cost, done, next_state):
        """
        向缓冲区中添加一条新的经验数据。

        参数:
        state (np.ndarray): 当前状态，类型为 NumPy 数组。
        action (np.ndarray): 执行的动作，类型为 NumPy 数组。
        reward (float): 获得的奖励。
        cost (float): 产生的代价。
        done (bool): 回合是否结束的标志。
        next_state (np.ndarray): 下一个状态，类型为 NumPy 数组。
        """
        # 将当前状态复制到缓冲区的当前插入位置
        self.states[self._p].copy_(torch.from_numpy(state))
        # 将执行的动作复制到缓冲区的当前插入位置
        self.actions[self._p].copy_(torch.from_numpy(action))
        # 将获得的奖励存储到缓冲区的当前插入位置
        self.rewards[self._p] = float(reward)
        # 将产生的代价存储到缓冲区的当前插入位置
        self.costs[self._p] = float(cost)
        # 将回合结束标志存储到缓冲区的当前插入位置
        self.dones[self._p] = float(done)
        # 将下一个状态复制到缓冲区的当前插入位置
        self.next_states[self._p].copy_(torch.from_numpy(next_state))

        # 更新插入位置的索引，使用取模运算实现循环缓冲区
        self._p = (self._p + 1) % self.buffer_size
        # 更新当前缓冲区中存储的经验数量，不超过缓冲区的最大容量
        self._n = min(self._n + 1, self.buffer_size)

    def sample(self, batch_size):
        """
        从缓冲区中随机采样一批经验数据。

        参数:
        batch_size (int): 采样的经验数据数量。

        返回:
        tuple: 包含采样的状态、动作、奖励、代价、回合结束标志和下一个状态的元组。
        """
        # 随机生成一批索引，范围从 0 到当前缓冲区中存储的经验数量
        idxes = np.random.randint(low=0, high=self._n, size=batch_size)
        return (
            # 根据索引采样状态
            self.states[idxes],
            # 根据索引采样动作
            self.actions[idxes],
            # 根据索引采样奖励
            self.rewards[idxes],
            # 根据索引采样代价
            self.costs[idxes],
            # 根据索引采样回合结束标志
            self.dones[idxes],
            # 根据索引采样下一个状态
            self.next_states[idxes]
        )
