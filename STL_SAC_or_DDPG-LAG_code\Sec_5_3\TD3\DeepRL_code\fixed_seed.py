import numpy as np
import torch
import random

def fixed_seed_function(seed):
    """
    设置固定的随机种子，确保实验的可重复性。

    参数:
    seed (int): 要设置的随机种子值。
    """
    # 设置 Python 内置 random 模块的随机种子
    random.seed(seed)
    # 设置 Numpy 的随机种子
    np.random.seed(seed)
    # 设置 Pytorch 的 CPU 随机种子
    torch.manual_seed(seed)
    # 设置 Pytorch 的所有 GPU 随机种子
    torch.cuda.manual_seed_all(seed)
    # 将 PyTorch 的 cuDNN 库设置为确定性模式，确保卷积操作结果可重复
    torch.backends.cudnn.deterministic = True
