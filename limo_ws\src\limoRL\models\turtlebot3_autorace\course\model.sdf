<?xml version="1.0" ?>
<sdf version="1.6">
  <model name="course">
    <static>true</static>
    <link name="course_link">
      <collision name="course_collision">
        <geometry>
          <plane>
            <normal>0 0 1</normal>
            <size>4 4</size>
          </plane>
        </geometry>
        <surface>
          <friction>
            <ode>
              <mu>100</mu>
              <mu2>50</mu2>
            </ode>
          </friction>
        </surface>
      </collision>
      <visual name="course_visual">
        <cast_shadows>false</cast_shadows>
        <geometry>
	        <plane>
            <normal>0 0 1</normal>
            <size>4 4</size>
          </plane>
        </geometry>
	      <material>
          <script>
            <!--uri>file://materials/scripts/floor.material</uri-->
            <uri>model://turtlebot3_autorace/course/materials/scripts</uri>
            <uri>model://turtlebot3_autorace/course/materials/textures</uri>
            <name>course</name>
          </script>
        </material>
      </visual>
    </link>
  </model>
</sdf>

