# 导入 numpy 库，用于数值计算
import numpy as np
# 导入 PyTorch 库，用于深度学习相关操作
import torch
# 从 torch 模块中导入神经网络模块
from torch import nn
# 从 torch.distributions 模块中导入正态分布类
from torch.distributions import Normal
# 导入 PyTorch 的函数式接口
import torch.nn.functional as F

# 导入回放缓冲区模块
import replay_memory
# 导入网络模块，包含 SAC 算法所需的网络结构
import network
# 导入训练器模块，包含算法基类
import trainer

class SAC(trainer.Algorithm):
    """
    实现软演员 - 评论家（Soft Actor-Critic, SAC）算法的类，继承自 trainer.Algorithm 基类。
    SAC 是一种基于策略梯度的无模型强化学习算法，结合了最大熵强化学习的思想。
    """

    def __init__(self, state_shape, action_shape, device=torch.device('cuda'), seed=0,
                batch_size=256, gamma=0.99, lr_actor=3e-4, lr_critic=3e-4, lr_entropy=3e-4,
                replay_size=10**6, start_steps=10**4, tau=0.01, alpha=1.0, reward_scale=1.0, auto_coef=False):
        """
        初始化 SAC 算法实例。

        :param state_shape: 状态的形状，用于确定网络输入维度。
        :param action_shape: 动作的形状，用于确定网络输出维度。
        :param device: 计算设备，默认为 CUDA 设备。
        :param seed: 随机数种子，默认为 0。
        :param batch_size: 训练时每次采样的样本数量，默认为 256。
        :param gamma: 折扣因子，用于计算累积奖励，默认为 0.99。
        :param lr_actor: 演员网络的学习率，默认为 3e-4。
        :param lr_critic: 评论家网络的学习率，默认为 3e-4。
        :param lr_entropy: 熵系数的学习率，默认为 3e-4。
        :param replay_size: 回放缓冲区的大小，默认为 10^6。
        :param start_steps: 开始训练前的探索步数，默认为 10^4。
        :param tau: 目标网络软更新的系数，默认为 0.01。
        :param alpha: 熵系数，控制探索与利用的平衡，默认为 1.0。
        :param reward_scale: 奖励缩放因子，默认为 1.0。
        :param auto_coef: 是否自动调整熵系数，默认为 False。
        """
        # 调用父类的构造函数
        super().__init__()

        # 初始化回放缓冲区
        self.buffer = replay_memory.ReplayBuffer(
            buffer_size=replay_size,  # 回放缓冲区的最大容量
            state_shape=state_shape,  # 状态的形状
            action_shape=action_shape,  # 动作的形状
            device=device,  # 存储数据的设备
        )

        # 构建 SAC 算法所需的深度神经网络
        self.actor = network.SACActor(
            state_shape=state_shape,  # 状态的形状，作为输入维度
            action_shape=action_shape  # 动作的形状，作为输出维度
        ).to(device)  # 将演员网络移动到指定设备
        self.critic = network.SACCritic(
            state_shape=state_shape,  # 状态的形状，作为输入维度
            action_shape=action_shape  # 动作的形状，作为输入维度
        ).to(device)  # 将评论家网络移动到指定设备
        self.critic_target = network.SACCritic(
            state_shape=state_shape,  # 状态的形状，作为输入维度
            action_shape=action_shape  # 动作的形状，作为输入维度
        ).to(device).eval()  # 将目标评论家网络移动到指定设备并设置为评估模式

        # 是否自动调整熵系数
        self.auto_coef = auto_coef

        self.alpha = alpha  # 熵系数
        if self.auto_coef:  # 如果需要自动调整熵系数
            # 计算目标熵，目标熵为动作维度的负值
            self.target_entropy = -torch.prod(torch.Tensor(action_shape).to(device)).item()
            # 初始化可训练的对数熵系数
            self.log_alpha = torch.zeros(1, requires_grad=True, device=device)

        # 初始化目标网络，将评论家网络的参数复制到目标评论家网络
        self.critic_target.load_state_dict(self.critic.state_dict())
        # 冻结目标评论家网络的参数，不进行梯度更新
        for param in self.critic_target.parameters():
            param.requires_grad = False

        # 定义优化器
        # 演员网络的优化器，使用 Adam 优化算法
        self.optim_actor = torch.optim.Adam(self.actor.parameters(), lr=lr_actor)
        # 评论家网络的优化器，使用 Adam 优化算法
        self.optim_critic = torch.optim.Adam(self.critic.parameters(), lr=lr_critic)
        if self.auto_coef:
            # 熵系数的优化器，使用 Adam 优化算法
            self.optim_alpha = torch.optim.Adam([self.log_alpha], lr=lr_entropy)  # 3e-4

        # 其他参数
        self.learning_steps = 0  # 学习步数计数器
        self.batch_size = batch_size  # 训练批次大小
        self.device = device  # 计算设备
        self.gamma = gamma  # 折扣因子
        self.start_steps = start_steps  # 开始训练前的探索步数
        self.tau = tau  # 目标网络软更新的系数
        self.reward_scale = reward_scale  # 奖励缩放因子

    def explore(self, state):
        """
        在探索模式下，选择一个随机动作并返回该动作及其概率密度的对数。
        探索模式通常在训练初期使用，引入随机性以帮助智能体发现更多的状态 - 动作对。

        :param state: 当前环境的状态，通常为一个数组或张量。
        :return: 元组，包含 numpy 数组形式的随机动作和该动作概率密度的对数。
        """
        # 将输入的状态转换为 PyTorch 张量，指定数据类型为 float，并移动到指定设备上
        # 使用 unsqueeze_(0) 在第 0 维添加一个维度，将状态转换为批量大小为 1 的张量
        state = torch.tensor(state, dtype=torch.float, device=self.device).unsqueeze_(0)
        # 不计算梯度，减少计算开销，因为在探索阶段不需要进行反向传播
        with torch.no_grad():
            # 调用演员网络的 sample 方法，生成随机动作及其概率密度的对数
            action, log_pi = self.actor.sample(state)
        # 将动作从 PyTorch 张量转换为 numpy 数组，并去除批量维度
        # 将动作概率密度的对数转换为 Python 标量
        return action.cpu().numpy()[0], log_pi.item()
    
    def exploit(self, state):
        """
        在利用模式下，选择当前策略认为的最优确定性动作。
        利用模式通常在测试或评估阶段使用，以最大化累积奖励。

        :param state: 当前环境的状态，通常为一个数组或张量。
        :return: numpy 数组形式的确定性动作。
        """
        # 将输入的状态转换为 PyTorch 张量，指定数据类型为 float，并移动到指定设备上
        # 使用 unsqueeze_(0) 在第 0 维添加一个维度，将状态转换为批量大小为 1 的张量
        state = torch.tensor(state, dtype=torch.float, device=self.device).unsqueeze_(0)
        # 不计算梯度，减少计算开销，因为在利用阶段不需要进行反向传播
        with torch.no_grad():
            # 调用演员网络，直接得到确定性动作
            action = self.actor(state)
        # 将动作从 PyTorch 张量转换为 numpy 数组，并去除批量维度
        return action.cpu().numpy()[0]

    def is_update(self, steps):
        """
        判断是否满足算法参数更新的条件。
        在学习开始的一段时间内（start_steps），不进行学习更新，以确保回放缓冲区有足够的数据。

        :param steps: 当前的训练步数。
        :return: 布尔值，若满足更新条件返回 True，否则返回 False。
        """
        # 学习开始的一定步数（start_steps）内不进行学习，且回放缓冲区的样本数需足够一个批次
        return steps >= max(self.start_steps, self.batch_size)
    def step(self, env, state, t, steps):
        """
        执行一个时间步的交互操作，与环境进行一次交互，更新状态并将经验添加到回放缓冲区。

        :param env: 环境实例，用于与智能体进行交互。
        :param state: 当前环境的状态。
        :param t: 当前回合的时间步计数器。
        :param steps: 当前的训练总步数。
        :return: 元组，包含下一个状态和更新后的回合时间步计数器。
        """
        # 获取环境中一个回合的最大时间步数
        max_episode_steps = env.spec.max_episode_steps
        # 当前回合的时间步计数器加 1
        t += 1

        # 如果当前训练步数小于等于开始训练前的探索步数
        if steps <= self.start_steps:
            # 从环境的动作空间中随机采样一个动作
            action = env.action_space.sample()
        else:
            # 调用 explore 方法，根据当前策略选择一个探索性动作
            action, _ = self.explore(state)
        # 在环境中执行动作，获取下一个状态、奖励、回合结束标志等信息
        next_state, reward, done, _ = env.step(action)

        # 如果当前时间步达到一个回合的最大时间步数
        if t == max_episode_steps:
            # 标记回合未真正结束，避免在回放缓冲区中错误地终止该轨迹
            done_masked = False
        else:
            # 否则，使用环境返回的回合结束标志
            done_masked = done

        # 将当前的经验（状态、动作、奖励、是否结束标志、下一个状态）添加到回放缓冲区
        self.buffer.append(state, action, reward, done_masked, next_state)

        # 如果回合结束
        if done:
            # 重置当前回合的时间步计数器
            t = 0
            # 重置环境，获取新的初始状态
            next_state = env.reset()

        # 返回下一个状态和更新后的回合时间步计数器
        return next_state, t

    def update(self):
        """
        更新 SAC 算法的网络参数，包括评论家网络、演员网络和熵系数（如果启用自动调整），
        并更新目标评论家网络。
        """
        # 学习步数计数器加 1
        self.learning_steps += 1
        # 从回放缓冲区中随机采样一个批次的数据
        states, actions, rewards, dones, next_states = self.buffer.sample(self.batch_size)

        # 更新评论家网络的参数
        self.update_critic(states, actions, rewards, dones, next_states)
        # 更新演员网络的参数
        self.update_actor(states)
        # 如果启用了自动调整熵系数
        if self.auto_coef:
            # 更新熵系数
            self.update_entropy_coef(states)
        # 软更新目标评论家网络的参数
        self.update_target()
    
    def update_critic(self, states, actions, rewards, dones, next_states):
        """
        更新评论家网络的参数。

        :param states: 当前状态的批量数据。
        :param actions: 当前动作的批量数据。
        :param rewards: 奖励的批量数据。
        :param dones: 回合结束标志的批量数据。
        :param next_states: 下一个状态的批量数据。
        """
        # 通过评论家网络计算当前状态 - 动作对的 Q 值
        curr_qs1, curr_qs2 = self.critic(states, actions)

        # 不计算梯度，减少计算开销，因为在计算目标 Q 值时不需要反向传播
        with torch.no_grad():
            # 从演员网络采样下一个动作及其概率密度的对数
            next_actions, log_pis = self.actor.sample(next_states)
            # 通过目标评论家网络计算下一个状态 - 动作对的 Q 值
            next_qs1, next_qs2 = self.critic_target(next_states, next_actions)
            # 取两个目标 Q 值的最小值，并减去熵正则化项
            next_qs = torch.min(next_qs1, next_qs2) - self.alpha * log_pis
        # 计算目标 Q 值，结合奖励、折扣因子和下一个状态的 Q 值
        target_qs = rewards * self.reward_scale + (1.0 - dones) * self.gamma * next_qs

        # 计算第一个评论家网络的损失，使用均方误差
        loss_critic1 = (curr_qs1 - target_qs).pow_(2).mean()
        # 计算第二个评论家网络的损失，使用均方误差
        loss_critic2 = (curr_qs2 - target_qs).pow_(2).mean()

        # 清空评论家网络优化器的梯度
        self.optim_critic.zero_grad()
        # 计算总损失的梯度
        (loss_critic1 + loss_critic2).backward(retain_graph=False)
        # 更新评论家网络的参数
        self.optim_critic.step()


    def update_actor(self, states):
        """
        更新演员网络的参数，目标是最大化累积奖励和策略熵。

        :param states: 当前状态的批量数据。
        """
        # 从演员网络采样动作及其概率密度的对数
        actions, log_pis = self.actor.sample(states)
        # 通过评论家网络计算当前状态 - 动作对的 Q 值
        qs1, qs2 = self.critic(states, actions)
        # 计算演员网络的损失，目标是最大化 Q 值并增加策略的熵
        # 损失函数为 熵正则化项减去 Q 值的最小值，取平均值
        loss_actor = (self.alpha * log_pis - torch.min(qs1, qs2)).mean()

        # 清空演员网络优化器的梯度
        self.optim_actor.zero_grad()
        # 计算损失函数的梯度
        loss_actor.backward(retain_graph=False)
        # 更新演员网络的参数
        self.optim_actor.step()

    def update_entropy_coef(self, states):
        """
        如果启用了自动调整熵系数，更新熵系数。

        :param states: 当前状态的批量数据。
        """
        # 从演员网络采样动作及其概率密度的对数
        _, log_pis = self.actor.sample(states)

        # 计算熵系数的损失，目标是使策略的熵接近目标熵
        alpha_loss = -(self.log_alpha * (log_pis + self.target_entropy).detach()).mean()

        # 清空熵系数优化器的梯度
        self.optim_alpha.zero_grad()
        # 计算损失函数的梯度
        alpha_loss.backward()
        # 更新熵系数的参数
        self.optim_alpha.step()

        # 通过指数运算得到更新后的熵系数
        self.alpha = self.log_alpha.exp()

    def update_target(self):
        """
        软更新目标评论家网络的参数，使目标网络的参数缓慢接近当前评论家网络的参数。
        """
        # 遍历目标评论家网络和当前评论家网络的参数
        for t, s in zip(self.critic_target.parameters(), self.critic.parameters()):
            # 使用软更新公式更新目标网络的参数
            # 新参数 = (1 - tau) * 旧参数 + tau * 当前网络参数
            t.data.mul_(1.0 - self.tau)
            t.data.add_(self.tau * s.data)
    
    def backup_model(self, steps):
        """
        备份当前的演员网络和评论家网络的参数到文件中。

        :param steps: 当前的训练步数，用于生成文件名。
        """
        # 保存演员网络的参数到文件
        torch.save(self.actor.state_dict(), 'SAC_STL_actor_' + str(steps) + '.pth')
        # 保存评论家网络的参数到文件
        torch.save(self.critic.state_dict(), 'SAC_STL_critic_' + str(steps) + '.pth')

        






