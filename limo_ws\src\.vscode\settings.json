{"python.autoComplete.extraPaths": ["/home/<USER>/turtlebot3_ws/devel/lib/python3/dist-packages", "/home/<USER>/catkin_ws/devel/lib/python3/dist-packages", "/opt/ros/noetic/lib/python3/dist-packages"], "python.analysis.extraPaths": ["/home/<USER>/turtlebot3_ws/devel/lib/python3/dist-packages", "/home/<USER>/catkin_ws/devel/lib/python3/dist-packages", "/opt/ros/noetic/lib/python3/dist-packages"], "files.associations": {"cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "hash_map": "cpp", "hash_set": "cpp", "strstream": "cpp", "bit": "cpp", "*.tcc": "cpp", "bitset": "cpp", "chrono": "cpp", "codecvt": "cpp", "complex": "cpp", "condition_variable": "cpp", "cstdint": "cpp", "deque": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "cfenv": "cpp", "cinttypes": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "valarray": "cpp", "variant": "cpp", "*.ipp": "cpp"}}