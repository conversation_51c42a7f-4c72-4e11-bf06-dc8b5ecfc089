import torch
from torch import nn
import torch.nn.functional as F

class TD3Actor(nn.Module):
    """
    TD3 算法中的演员（Actor）网络，用于根据输入状态生成动作。
    网络输出的动作经过 tanh 函数处理，将动作值限制在 [-1, 1] 范围内。
    """
    def __init__(self, state_shape, action_shape):
        """
        初始化 TD3Actor 网络。

        参数:
        state_shape (tuple): 输入状态的形状，通常为 (state_dim,)。
        action_shape (tuple): 输出动作的形状，通常为 (action_dim,)。
        """
        super().__init__()
        # 定义神经网络结构
        self.net = nn.Sequential(
            # 第一层全连接层，输入维度为状态维度，输出维度为 256
            nn.Linear(state_shape[0], 256),
            # ReLU 激活函数，inplace=True 表示直接在原张量上进行操作，节省内存
            nn.ReLU(inplace=True),
            # 第二层全连接层，输入维度为 256，输出维度为 256
            nn.Linear(256, 256),
            # ReLU 激活函数
            nn.ReLU(inplace=True),
            # 第三层全连接层，输入维度为 256，输出维度为动作维度
            nn.Linear(256, action_shape[0]),
        )

    def forward(self, states):
        """
        前向传播函数，根据输入状态计算动作。

        参数:
        states (torch.Tensor): 输入的状态张量，形状为 (batch_size, state_dim)。

        返回:
        torch.Tensor: 输出的动作张量，形状为 (batch_size, action_dim)，值在 [-1, 1] 范围内。
        """
        return torch.tanh(self.net(states))

    def sample(self, states):
        """
        根据输入状态采样动作。在当前实现中，直接调用 forward 方法获取确定性动作。

        参数:
        states (torch.Tensor): 输入的状态张量，形状为 (batch_size, state_dim)。

        返回:
        torch.Tensor: 采样得到的动作张量，形状为 (batch_size, action_dim)。
        """
        return self(states)
        
class TD3Critic(nn.Module):
    """
    TD3 算法中的评论家（Critic）网络，使用双 Q 网络结构，用于评估动作的价值。
    """
    def __init__(self, state_shape, action_shape):
        """
        初始化 TD3Critic 网络。

        参数:
        state_shape (tuple): 输入状态的形状，通常为 (state_dim,)。
        action_shape (tuple): 输入动作的形状，通常为 (action_dim,)。
        """
        super().__init__()
        # 定义第一个 Q 网络
        self.net1 = nn.Sequential(
            # 第一层全连接层，输入维度为状态维度与动作维度之和，输出维度为 256
            nn.Linear(state_shape[0] + action_shape[0], 256),
            # ReLU 激活函数
            nn.ReLU(inplace=True),
            # 第二层全连接层，输入维度为 256，输出维度为 256
            nn.Linear(256, 256),
            # ReLU 激活函数
            nn.ReLU(inplace=True),
            # 第三层全连接层，输入维度为 256，输出维度为 1
            nn.Linear(256, 1),
        )
        # 定义第二个 Q 网络
        self.net2 = nn.Sequential(
            # 第一层全连接层，输入维度为状态维度与动作维度之和，输出维度为 256
            nn.Linear(state_shape[0] + action_shape[0], 256),
            # ReLU 激活函数
            nn.ReLU(inplace=True),
            # 第二层全连接层，输入维度为 256，输出维度为 256
            nn.Linear(256, 256),
            # ReLU 激活函数
            nn.ReLU(inplace=True),
            # 第三层全连接层，输入维度为 256，输出维度为 1
            nn.Linear(256, 1),
        )

    def forward(self, states, actions):
        """
        前向传播函数，根据输入状态和动作计算两个 Q 值。

        参数:
        states (torch.Tensor): 输入的状态张量，形状为 (batch_size, state_dim)。
        actions (torch.Tensor): 输入的动作张量，形状为 (batch_size, action_dim)。

        返回:
        tuple: 包含两个 Q 值的元组，每个 Q 值的形状为 (batch_size, 1)。
        """
        # 将状态和动作在最后一个维度上拼接
        x = torch.cat([states, actions], dim=-1)
        # 分别通过两个 Q 网络计算 Q 值
        return self.net1(x), self.net2(x)
