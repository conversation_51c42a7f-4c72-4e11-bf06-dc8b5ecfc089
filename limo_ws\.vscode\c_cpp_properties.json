{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/catkin_ws/devel/include/**", "/opt/ros/noetic/include/**", "/home/<USER>/catkin_ws/src/apriltag_ros/apriltag_ros/include/**", "/home/<USER>/catkin_ws/src/realsense/ddynamic_reconfigure/include/**", "/home/<USER>/catkin_ws/src/gtihub/guyue/include/**", "/home/<USER>/catkin_ws/src/study/guyue_action/include/**", "/home/<USER>/catkin_ws/src/kitti_stereo/include/**", "/home/<USER>/limo_ws/src/limo_bringup/include/**", "/home/<USER>/limo_ws/src/limo_dqn/include/**", "/home/<USER>/limo_ws/src/limo_gazebo_sim/include/**", "/home/<USER>/limo_ws/src/limo_park/include/**", "/home/<USER>/limo_ws/src/move_base_send_goal/include/**", "/home/<USER>/catkin_ws/src/robot_mrobot/mrobot_bringup/include/**", "/home/<USER>/catkin_ws/src/gtihub/multi_diffcar_ws/src/nmpc_ctr/include/**", "/home/<USER>/catkin_ws/src/open_manipulator/open_manipulator_control_gui/include/**", "/home/<USER>/catkin_ws/src/open_manipulator/open_manipulator_controller/include/**", "/home/<USER>/catkin_ws/src/open_manipulator/open_manipulator_libs/include/**", "/home/<USER>/catkin_ws/src/open_manipulator_applications/open_manipulator_master_slave/include/**", "/home/<USER>/catkin_ws/src/open_manipulator/open_manipulator_teleop/include/**", "/home/<USER>/catkin_ws/src/realsense/realsense-ros/realsense2_camera/include/**", "/home/<USER>/catkin_ws/src/open_manipulator_dependencies/roboticsgroup_gazebo_plugins/include/**", "/home/<USER>/catkin_ws/src/serl_franka_controllers/include/**", "/home/<USER>/catkin_ws/src/turtlebot3_simulations/turtlebot3_fake/include/**", "/home/<USER>/catkin_ws/src/turtlebot3_simulations/turtlebot3_gazebo/include/**", "/home/<USER>/catkin_ws/src/turtlebot3-master/turtlebot3_slam/include/**", "/home/<USER>/catkin_ws/src/wpr_simulation-master/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}