# 从 argparse 模块导入 Action 类，但后续未使用该导入项
from argparse import Action
import numpy as np
import torch
from torch import nn
from torch.distributions import Normal
import torch.nn.functional as F

# 导入重放内存模块
import replay_memory
# 导入初始状态内存模块
import init_state_memory
# 导入神经网络模块
import network
# 导入训练器模块
import trainer
# 从 ounoise 模块导入 OU 噪声类
from ounoise import OU_NOISE

class LagrangianTD3(trainer.Algorithm):
    """
    LagrangianTD3 类实现了基于拉格朗日乘数法的 Twin Delayed Deep Deterministic Policy Gradient (TD3) 算法。
    该算法结合了奖励和 STL（Signal Temporal Logic）奖励进行策略学习。
    """

    def __init__(self, state_shape, action_shape, device=torch.device('cuda'), seed=0,
                batch_size=64, gamma=0.99, lr_actor=3e-4, lr_critic=3e-4, lr_kappa=1e-5, threshold=-35.,
                pretrain_steps=250000, replay_size=10**6, start_steps=10**4, tau=0.01, policy_noise=0.2, noise_clip=0.5, policy_freq=2, reward_scale=1.0):
        """
        初始化 LagrangianTD3 类的实例。

        参数:
        state_shape (tuple): 状态的形状。
        action_shape (tuple): 动作的形状。
        device (torch.device): 计算设备，默认为 CUDA 设备。
        seed (int): 随机种子，默认为 0。
        batch_size (int): 训练时的批次大小，默认为 64。
        gamma (float): 折扣因子，默认为 0.99。
        lr_actor (float): 演员网络的学习率，默认为 3e-4。
        lr_critic (float): 评论家网络的学习率，默认为 3e-4。
        lr_kappa (float): 拉格朗日乘数的学习率，默认为 1e-5。
        threshold (float): 拉格朗日约束的阈值，默认为 -35.。
        pretrain_steps (int): 预训练步数，默认为 250000。
        replay_size (int): 重放缓冲区的大小，默认为 10**6。
        start_steps (int): 开始学习前的随机探索步数，默认为 10**4。
        tau (float): 目标网络软更新的系数，默认为 0.01。
        policy_noise (float): 策略噪声的标准差，默认为 0.2。
        noise_clip (float): 策略噪声的裁剪范围，默认为 0.5。
        policy_freq (int): 策略更新的频率，默认为 2。
        reward_scale (float): 奖励的缩放因子，默认为 1.0。
        """
        super().__init__()

        # 初始化重放缓冲区
        self.buffer = replay_memory.ReplayBuffer(
            buffer_size=replay_size,
            state_shape=state_shape,
            action_shape=action_shape,
            device=device,
        )

        # 初始化用于存储初始状态的重放缓冲区
        self.init_state_buffer = init_state_memory.InitStateBuffer(
            buffer_size=replay_size,
            state_shape=state_shape,
            device=device,
        )

        # 初始化演员网络
        self.actor = network.TD3Actor(
            state_shape=state_shape,
            action_shape=action_shape
        ).to(device)

        # 初始化目标演员网络
        self.actor_target = network.TD3Actor(
            state_shape=state_shape,
            action_shape=action_shape
        ).to(device).eval()

        # 初始化奖励评论家网络
        self.reward_critic = network.TD3Critic(
            state_shape=state_shape,
            action_shape=action_shape
        ).to(device)

        # 初始化目标奖励评论家网络
        self.reward_critic_target = network.TD3Critic(
            state_shape=state_shape,
            action_shape=action_shape
        ).to(device).eval()

        # 初始化 STL 评论家网络
        self.STL_critic = network.TD3Critic(
            state_shape=state_shape,
            action_shape=action_shape
        ).to(device)

        # 初始化目标 STL 评论家网络
        self.STL_critic_target = network.TD3Critic(
            state_shape=state_shape,
            action_shape=action_shape
        ).to(device).eval()

        # 拉格朗日乘数
        self.threshold = threshold  # 约束阈值，默认为 -40.0
        self.log_kappa = torch.tensor(0.0, requires_grad=True, device=device) 
        self.kappa = self.log_kappa.exp()  # 初始值为 1.0

        # 初始化目标神经网络（奖励评论家网络和 STL 奖励评论家网络）
        self.actor_target.load_state_dict(self.actor.state_dict())
        for param in self.actor_target.parameters():  # 切断梯度信息
            param.requires_grad = False
        self.reward_critic_target.load_state_dict(self.reward_critic.state_dict())
        for param in self.reward_critic_target.parameters():  # 切断梯度信息
            param.requires_grad = False
        self.STL_critic_target.load_state_dict(self.STL_critic.state_dict())
        for param in self.STL_critic_target.parameters():  # 切断梯度信息
            param.requires_grad = False

        # 定义优化器（Adam）
        self.optim_actor = torch.optim.Adam(self.actor.parameters(), lr=lr_actor)
        self.optim_reward_critic = torch.optim.Adam(self.reward_critic.parameters(), lr=lr_critic)
        self.optim_STL_critic = torch.optim.Adam(self.STL_critic.parameters(), lr=lr_critic)
        self.optim_kappa = torch.optim.Adam([self.log_kappa], lr=lr_kappa) 

        # OU 噪声，用于探索
        self.ounoise = OU_NOISE(action_shape)
        
        # 其他超参数
        self.learning_steps = 0  # 学习步数计数器
        self.batch_size = batch_size
        self.pretrain_steps = pretrain_steps
        self.device = device
        self.gamma = gamma
        self.start_steps = start_steps
        self.tau = tau
        self.policy_noise = policy_noise  # TD3 参数
        self.noise_clip = noise_clip  # TD3 参数
        self.policy_freq = policy_freq  # TD3 参数
        self.reward_scale = reward_scale

    def explore(self, state):
        """
        在探索模式下选择动作，为动作添加 OU 噪声。

        参数:
        state (np.ndarray): 当前状态。

        返回:
        np.ndarray: 添加噪声后的动作。
        """
        state = torch.tensor(state, dtype=torch.float, device=self.device).unsqueeze_(0)
        with torch.no_grad():
            action = self.actor.sample(state)
        action += torch.tensor(self.ounoise.noise(), dtype=torch.float, device=self.device)
        action = action.cpu().numpy()[0]
        action = np.clip(action, -1, 1)
        return action
    
    def exploit(self, state):
        """ 
        返回一个确定性动作（均值）。 

        参数:
        state (np.ndarray): 当前状态。

        返回:
        np.ndarray: 确定性动作。
        """
        state = torch.tensor(state, dtype=torch.float, device=self.device).unsqueeze_(0)
        with torch.no_grad():
            action = self.actor(state)
        return action.cpu().numpy()[0]

    def is_update(self, steps):
        """
        判断是否达到更新策略的条件。

        参数:
        steps (int): 当前的步数。

        返回:
        bool: 是否达到更新条件。
        """
        # 代理在一定时期（start_steps）内不学习其策略
        return steps >= max(self.start_steps, self.batch_size)
    
    def init_state_stock(self, init_state):
        """
        将从初始状态密度采样得到的状态数据添加到初始状态缓冲区。

        参数:
        init_state (np.ndarray): 初始状态。
        """
        # 添加从初始状态密度采样得到的状态数据
        self.init_state_buffer.append(init_state)

    def step(self, env, state, t, steps):
        """
        执行一步探索操作。

        参数:
        env (object): 环境对象。
        state (np.ndarray): 当前状态。
        t (int): 当前的时间步。
        steps (int): 当前的总步数。

        返回:
        tuple: 下一个状态和更新后的时间步。
        """
        if t == 0:
            self.init_state_buffer.append(state)

        t += 1

        # 在一定时期（start_steps）内，代理随机选择动作以收集多样化的数据
        if steps <= self.start_steps:
            action = env.action_space.sample()
        else:
            action = self.explore(state)
        next_state, reward, stl_reward, done, _ = env.step(action)

        if t == env._max_episode_steps:
            done_masked = False
        else:
            done_masked = done

        # 将经验添加到重放缓冲区
        self.buffer.append(state, action, reward, stl_reward, done_masked, next_state)

        # 在回合结束时重置环境
        if done:
            t = 0
            next_state = env.reset()
            self.ounoise.reset()

        return next_state, t

    def update(self, steps):
        """
        主算法，更新网络参数。

        参数:
        steps (int): 当前的总步数。
        """
        self.learning_steps += 1
        states, actions, rewards, stl_rewards, dones, next_states = self.buffer.sample(self.batch_size)

        self.update_reward_critic(states, actions, rewards, dones, next_states)

        if steps == self.pretrain_steps:
            print("===== END PRETRAIN =====")
        
        if steps < self.pretrain_steps:
            self.update_pretrain_STL_critic(states, actions, stl_rewards, dones, next_states)
            if self.learning_steps % self.policy_freq == 0:  # TD3
                self.update_pretrain_actor(states)
        else:
            self.update_finetune_STL_critic(states, actions, stl_rewards, dones, next_states)
            if self.learning_steps % self.policy_freq == 0:  # TD3
                self.update_finetune_actor(states)

        if steps >= self.pretrain_steps:   
            init_states = self.init_state_buffer.sample(self.batch_size) 
            self.update_kappa(init_states)

        if self.learning_steps % self.policy_freq == 0:  # TD3
            self.update_target()       
    
    def update_reward_critic(self, states, actions, rewards, dones, next_states):
        """
        更新奖励评论家网络。

        参数:
        states (torch.Tensor): 状态张量。
        actions (torch.Tensor): 动作张量。
        rewards (torch.Tensor): 奖励张量。
        dones (torch.Tensor): 结束标志张量。
        next_states (torch.Tensor): 下一个状态张量。
        """
        curr_qs1, curr_qs2 = self.reward_critic(states, actions)  # 双 Q 学习

        with torch.no_grad():
            # 裁剪噪声
            noises = (
                torch.randn_like(actions) * self.policy_noise
            ).clamp(-self.noise_clip, self.noise_clip)
            
            # next_actions = self.actor_target.sample(next_states)
            next_actions = self.actor_target(next_states)
            next_actions = (next_actions + noises).clamp(-1.0,1.0)

            next_qs1, next_qs2 = self.reward_critic_target(next_states, next_actions)
            next_qs = torch.min(next_qs1, next_qs2)

        target_qs = rewards * self.reward_scale + (1.0 - dones) * self.gamma * next_qs

        loss_critic1 = (curr_qs1 - target_qs).pow_(2).mean()
        loss_critic2 = (curr_qs2 - target_qs).pow_(2).mean()

        self.optim_reward_critic.zero_grad()
        (loss_critic1 + loss_critic2).backward(retain_graph=False)
        self.optim_reward_critic.step()

    def update_pretrain_STL_critic(self, states, actions, stl_rewards, dones, next_states):
        """
        预训练阶段更新 STL 评论家网络。

        参数:
        states (torch.Tensor): 状态张量。
        actions (torch.Tensor): 动作张量。
        stl_rewards (torch.Tensor): STL 奖励张量。
        dones (torch.Tensor): 结束标志张量。
        next_states (torch.Tensor): 下一个状态张量。
        """
        curr_stl_qs1, curr_stl_qs2 = self.STL_critic(states, actions)

        with torch.no_grad():
            # 裁剪噪声
            noises = (
                torch.randn_like(actions) * self.policy_noise
            ).clamp(-self.noise_clip, self.noise_clip)

            # next_actions = self.actor_target.sample(next_states)
            next_actions = self.actor_target(next_states)
            next_actions = (next_actions + noises).clamp(-1.0,1.0)

            next_stl_qs1, next_stl_qs2 = self.STL_critic_target(next_states, next_actions)
            next_stl_qs = torch.min(next_stl_qs1, next_stl_qs2)

        target_stl_qs = stl_rewards * self.reward_scale + (1.0 - dones) * self.gamma * next_stl_qs

        loss_stl_critic1 = (curr_stl_qs1 - target_stl_qs).pow_(2).mean()
        loss_stl_critic2 = (curr_stl_qs2 - target_stl_qs).pow_(2).mean()

        self.optim_STL_critic.zero_grad()
        (loss_stl_critic1 + loss_stl_critic2).backward(retain_graph=False)
        self.optim_STL_critic.step()

    def update_finetune_STL_critic(self, states, actions, stl_rewards, dones, next_states):
        """
        微调阶段更新 STL 评论家网络，与预训练阶段更新逻辑相同。

        参数:
        states (torch.Tensor): 状态张量。
        actions (torch.Tensor): 动作张量。
        stl_rewards (torch.Tensor): STL 奖励张量。
        dones (torch.Tensor): 结束标志张量。
        next_states (torch.Tensor): 下一个状态张量。
        """
        curr_stl_qs1, curr_stl_qs2 = self.STL_critic(states, actions)

        with torch.no_grad():
            # 裁剪噪声
            noises = (
                torch.randn_like(actions) * self.policy_noise
            ).clamp(-self.noise_clip, self.noise_clip)

            # next_actions = self.actor_target.sample(next_states)
            next_actions = self.actor_target(next_states)
            next_actions = (next_actions + noises).clamp(-1.0,1.0)

            next_stl_qs1, next_stl_qs2 = self.STL_critic_target(next_states, next_actions)
            next_stl_qs = torch.min(next_stl_qs1, next_stl_qs2)

        target_stl_qs = stl_rewards * self.reward_scale + (1.0 - dones) * self.gamma * next_stl_qs

        loss_stl_critic1 = (curr_stl_qs1 - target_stl_qs).pow_(2).mean()
        loss_stl_critic2 = (curr_stl_qs2 - target_stl_qs).pow_(2).mean()

        self.optim_STL_critic.zero_grad()
        (loss_stl_critic1 + loss_stl_critic2).backward(retain_graph=False)
        self.optim_STL_critic.step()

    def update_pretrain_actor(self, states):
        """
        预训练阶段更新演员网络。

        参数:
        states (torch.Tensor): 状态张量。
        """
        # actions = self.actor.sample(states)
        actions = self.actor(states)
        STL_qs1, STL_qs2 = self.STL_critic(states, actions)
        STL_qs = torch.min(STL_qs1, STL_qs2)
        loss_actor = -STL_qs.mean()  # 预训练

        self.optim_actor.zero_grad()
        loss_actor.backward(retain_graph=False)
        self.optim_actor.step()

    def update_finetune_actor(self, states):
        """
        微调阶段更新演员网络。

        参数:
        states (torch.Tensor): 状态张量。
        """
        # actions = self.actor.sample(states)
        actions = self.actor(states)
        reward_qs1, reward_qs2 = self.reward_critic(states, actions)
        reward_qs = torch.min(reward_qs1, reward_qs2)
        STL_qs1, STL_qs2 = self.STL_critic(states, actions)
        STL_qs = torch.min(STL_qs1, STL_qs2)
        loss_actor = -(reward_qs + self.kappa * STL_qs).mean()  # 微调

        self.optim_actor.zero_grad()
        loss_actor.backward(retain_graph=False)
        self.optim_actor.step()

    def update_kappa(self, states):
        """
        更新拉格朗日乘数。

        参数:
        states (torch.Tensor): 状态张量。
        """
        # actions = self.actor.sample(states)
        actions = self.actor(states)
        STL_qs1, STL_qs2 = self.STL_critic(states, actions)
        STL_qs = torch.min(STL_qs1, STL_qs2)

        kappa_loss = self.log_kappa.exp() * ((STL_qs - self.threshold).detach()).mean()
        # kappa_loss = self.log_kappa * ((STL_qs - self.threshold).detach()).mean()

        self.optim_kappa.zero_grad()
        kappa_loss.backward()
        self.optim_kappa.step()

        self.kappa = self.log_kappa.exp()

    def update_target(self):
        """
        更新目标网络的参数，使用软更新。
        """
        for t, s in zip(self.actor_target.parameters(), self.actor.parameters()):
            t.data.mul_(1.0 - self.tau)
            t.data.add_(self.tau * s.data)
        for t, s in zip(self.reward_critic_target.parameters(), self.reward_critic.parameters()):
            t.data.mul_(1.0 - self.tau)
            t.data.add_(self.tau * s.data)
        for t, s in zip(self.STL_critic_target.parameters(), self.STL_critic.parameters()):
            t.data.mul_(1.0 - self.tau)
            t.data.add_(self.tau * s.data)
    
    def backup_model(self, steps):
        """
        备份模型参数。

        参数:
        steps (int): 当前的总步数。
        """
        # model = self.actor.to('cpu')
        torch.save(self.actor.state_dict(), 'TD3_STL_Actor_' + str(steps) + '.pth')
        torch.save(self.reward_critic.state_dict(), 'TD3_Reward_Critic_' + str(steps) + '.pth')
        torch.save(self.STL_critic.state_dict(), 'TD3_STL_Critic_' + str(steps) + '.pth')
