<?xml version='1.0'?>
<sdf version='1.6'>
  <model name='turtlebot3_plaza'>
    <pose frame=''>0 0 0 0 -0 0</pose>
    <link name='Wall_0'>
      <collision name='Wall_0_Collision'>
        <geometry>
          <box>
            <size>5 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_0_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>5 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>-2.425 0 0 0 -0 1.5708</pose>
    </link>
    <link name='Wall_11'>
      <collision name='Wall_11_Collision'>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_11_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>-1.937 -1.467 0 0 -0 0</pose>
    </link>
    <link name='Wall_13'>
      <collision name='Wall_13_Collision'>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_13_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>-0.22 -1.866 0 0 0 -1.5708</pose>
    </link>
    <link name='Wall_15'>
      <collision name='Wall_15_Collision'>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_15_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>1.195 -1.002 0 0 -0 1.5708</pose>
    </link>
    <link name='Wall_17'>
      <collision name='Wall_17_Collision'>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_17_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>1.288 1.93 0 0 0 -1.5708</pose>
    </link>
    <link name='Wall_19'>
      <collision name='Wall_19_Collision'>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_19_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>1.91128 0.4632 0 0 -0 0</pose>
    </link>
    <link name='Wall_2'>
      <collision name='Wall_2_Collision'>
        <geometry>
          <box>
            <size>5 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_2_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>5 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>0 2.425 0 0 -0 0</pose>
    </link>
    <link name='Wall_21'>
      <collision name='Wall_21_Collision'>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_21_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>0.204 0.215 0 0 0 -1.5708</pose>
    </link>
    <link name='Wall_3'>
      <collision name='Wall_3_Collision'>
        <geometry>
          <box>
            <size>5 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_3_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>5 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>2.425 0 0 0 0 -1.5708</pose>
    </link>
    <link name='Wall_4'>
      <collision name='Wall_4_Collision'>
        <geometry>
          <box>
            <size>5 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_4_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>5 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>0 -2.425 0 0 -0 3.14159</pose>
    </link>
    <link name='Wall_7'>
      <collision name='Wall_7_Collision'>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_7_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>-1.064 1.548 0 0 -0 0</pose>
    </link>
    <link name='Wall_9'>
      <collision name='Wall_9_Collision'>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_9_Visual'>
        <pose frame=''>0 0 0.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>1 0.15 0.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
      </visual>
      <pose frame=''>-1.502 0.092 0 0 0 -1.5708</pose>
    </link>
    <static>1</static>
  </model>
</sdf>
