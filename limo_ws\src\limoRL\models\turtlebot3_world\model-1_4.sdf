<sdf version='1.4'>
    <!-- Draw Circle -->
    <model name='ros_symbol'>
      <static>1</static>
      <link name='symbol'>
        <collision name='one_one'>
          <pose>-1.1 -1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='one_one'>
          <pose>-1.1 -1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
          </material>
        </visual>

        <collision name='one_two'>
          <pose>-1.1 0 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='one_two'>
          <pose>-1.1 0 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
          </material>
        </visual>

        <collision name='one_three'>
          <pose>-1.1 1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='one_three'>
          <pose>-1.1 1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
          </material>
        </visual>

        <collision name='two_one'>
          <pose>0 -1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='two_one'>
          <pose>0 -1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
          </material>
        </visual>

        <collision name='two_two'>
          <pose>0 0 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='two_two'>
          <pose>0 0 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
          </material>
        </visual>

        <collision name='two_three'>
          <pose>0 1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='two_three'>
          <pose>0 1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
          </material>
        </visual>

        <collision name='three_one'>
          <pose>1.1 -1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='three_one'>
          <pose>1.1 -1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
          </material>
        </visual>

        <collision name='three_two'>
          <pose>1.1 0 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='three_two'>
          <pose>1.1 0 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
          </material>
        </visual>

        <collision name='three_three'>
          <pose>1.1 1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='three_three'>
          <pose>1.1 1.1 0.25 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.5</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
          </material>
        </visual>

        <!-- Draw Hexagon -->
        <collision name='head'>
          <pose>3.5 0 -0.5 0 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://turtlebot3_world/meshes/hexagon.dae</uri>
              <scale>0.8 0.8 0.8</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='head'>
          <pose>3.5 0 -0.5 0 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://turtlebot3_world/meshes/hexagon.dae</uri>
              <scale>0.8 0.8 0.8</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Green</name>
            </script>
          </material>
        </visual>

        <collision name='left_hand'>
          <pose>1.8 2.7 0 0 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://turtlebot3_world/meshes/hexagon.dae</uri>
              <scale>0.55 0.55 0.55</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='left_hand'>
          <pose>1.8 2.7 0 0 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://turtlebot3_world/meshes/hexagon.dae</uri>
              <scale>0.55 0.55 0.55</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Green</name>
            </script>
          </material>
        </visual>

        <collision name='right_hand'>
          <pose>1.8 -2.7 0 0 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://turtlebot3_world/meshes/hexagon.dae</uri>
              <scale>0.55 0.55 0.55</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='right_hand'>
          <pose>1.8 -2.7 0 0 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://turtlebot3_world/meshes/hexagon.dae</uri>
              <scale>0.55 0.55 0.55</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Green</name>
            </script>
          </material>
        </visual>

        <collision name='left_foot'>
          <pose>-1.8 2.7 0 0 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://turtlebot3_world/meshes/hexagon.dae</uri>
              <scale>0.55 0.55 0.55</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='left_foot'>
          <pose>-1.8 2.7 0 0 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://turtlebot3_world/meshes/hexagon.dae</uri>
              <scale>0.55 0.55 0.55</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Green</name>
            </script>
          </material>
        </visual>

        <collision name='right_foot'>
          <pose>-1.8 -2.7 0 0 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://turtlebot3_world/meshes/hexagon.dae</uri>
              <scale>0.55 0.55 0.55</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>

        <visual name='right_foot'>
          <pose>-1.8 -2.7 0 0 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://turtlebot3_world/meshes/hexagon.dae</uri>
              <scale>0.55 0.55 0.55</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Green</name>
            </script>
          </material>
        </visual>

        <!-- Draw Wall -->
        <collision name='body'>
        <pose>0 0 -0.3 0 0 -1.5708</pose>
        <geometry>
          <mesh>
            <uri>model://turtlebot3_world/meshes/wall.dae</uri>
            <scale>0.25 0.25 0.25</scale>
          </mesh>
        </geometry>
        <max_contacts>10</max_contacts>
        <surface>
          <bounce/>
          <friction>
            <ode/>
          </friction>
          <contact>
            <ode/>
          </contact>
        </surface>
      </collision>

      <visual name='body'>
        <pose>0 0 -0.3 0 0 -1.5708</pose>
        <geometry>
          <mesh>
            <uri>model://turtlebot3_world/meshes/wall.dae</uri>
            <scale>0.25 0.25 0.25</scale>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/FlatBlack</name>
          </script>
        </material>
      </visual>
    </link>
  </model>
</sdf>
